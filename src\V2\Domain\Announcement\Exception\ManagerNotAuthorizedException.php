<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Exception;

use App\Entity\Announcement;
use App\V2\Domain\Shared\Exception\NotAuthorizedException;

class ManagerNotAuthorizedException extends NotAuthorizedException
{
    protected $message = 'You do not have permission to view announcement managers';

    public static function userNotAuthorized(Announcement $announcement, string $user): self
    {
        return new self(
            \sprintf(
                'User %s is not authorized to perform this action for announcement %s',
                $user,
                $announcement->__toString()
            )
        );
    }

    public static function announcementManagerSharingIsDisabled(): self
    {
        return new self('Announcement manager sharing is disabled');
    }
}
