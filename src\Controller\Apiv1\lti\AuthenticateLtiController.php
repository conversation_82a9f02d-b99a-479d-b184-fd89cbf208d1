<?php

declare(strict_types=1);

namespace App\Controller\Apiv1\lti;

use App\Controller\Apiv1\ApiBaseController;
use App\Entity\User;
use App\V2\Domain\LTI\Exceptions\LtiUnauthorizedException;
use OAT\Library\Lti1p3Core\Registration\RegistrationInterface;
use OAT\Library\Lti1p3Core\Security\User\Result\UserAuthenticationResult;
use OAT\Library\Lti1p3Core\Security\User\Result\UserAuthenticationResultInterface;
use OAT\Library\Lti1p3Core\User\UserIdentity;

/**
 * @deprecated Migration to V2 required
 */
class AuthenticateLtiController extends ApiBaseController implements UserAuthenticatorInterface
{
    // use to test LTI course from admin
    private $testName = 'Soporte Gestionet';
    private $testEmail = '<EMAIL>';
    private $testLocale = 'es';
    private $isTest = false;

    public function authenticateV2(RegistrationInterface $registration, string $loginHint, $userId): UserAuthenticationResultInterface
    {
        $loginHintDecoded = json_decode(base64_decode($loginHint), true);
        $user = $this->entityManager->getRepository(User::class)->find($loginHintDecoded['id']);

        if (str_contains($_SERVER['REQUEST_URI'], 'admin/') || (int)$userId === 0) {
            $this->isTest = true;
        }

        if (null === $user) {
            throw new LtiUnauthorizedException();
        }

        return new UserAuthenticationResult(
            true,
            new UserIdentity(
                identifier: (string) $user->getId(),
                email: $user->getEmail(),
                givenName: $user->getFirstName(),
                familyName: $user->getLastName(),
                locale: $user->getLocaleCampus(),
            )
        );
    }
}
