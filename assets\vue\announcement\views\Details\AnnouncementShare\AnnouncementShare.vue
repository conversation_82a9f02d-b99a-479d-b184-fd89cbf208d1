<template>
  <div class="AnnouncementShare p-3 p-sm-0">
    <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loadingAnnouncementManagers">
      <spinner />
    </div>

    <div v-show="!loadingAnnouncementManagers">
      <div class="heading mb-3">
        <h5><b>{{ $t('ANNOUNCEMENT.SHARETAB.SUBTITLE') }}</b></h5>
        <button class="btn btn-sm btn-primary" data-bs-toggle="modal"
          data-bs-target="#AnnouncementManagerSelectionModal">
          <i class="fa fa-link"></i>
          {{ $t('ANNOUNCEMENT.SHARETAB.SHARE.BUTTON') }}
        </button>
      </div>

      <DataNotFound v-if="!announcementManagers.length" :hide-on="!announcementManagers.length" :text="$t('ANNOUNCEMENT.SHARETAB.NOT_FOUND') || ''"
        icon="fa-link" :banner="true" />

      <div v-else class="content">
        <table class="table manager-table">
          <thead>
            <tr>
              <th> {{ $t('SUBSCRIPTION.EMAIL') }} </th>
              <th> {{ $t('SUBSCRIPTION.NAME') }} </th>
              <th> {{ $t('SUBSCRIPTION.LAST_NAME') }} </th>
              <th class="auto-width"> {{ $t('ACTIONS') }} </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="manager in announcementManagers" :key="manager.id">
            <td class="user-email"> {{ manager.email }}</td>
            <td> {{ manager.name }}</td>
            <td> {{ manager.lastName }}</td>
            <td class="auto-width">
              <button class="btn btn-sm btn-danger" @click="handleManagerRemoval(manager.id)">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
          </tbody>
        </table>

      </div>
    </div>
    <AnnouncementManagerSelectionModal 
      :announcement="announcement" 
      :companyManagers="companyManagers"
      :excludedManagerIds="excludedManagerIds" 
      @searchRequested="loadCompanyManagers"
      @refreshAnnouncementManagers="loadAnnouncementManagers" />
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import DataNotFound from "../../../components/details/DataNotFound";
import Spinner from "../../../../admin/components/base/Spinner";
import AnnouncementManagerSelectionModal from "./AnnouncementManagerSelectionModal.vue";
import ItemDisplay from './ItemDisplay.vue';

export default {
  name: "AnnouncementShare",
  components: { Spinner, DataNotFound, AnnouncementManagerSelectionModal, ItemDisplay },
  data() {
    return {
      loadingAnnouncementManagers: true,
      announcementManagers: [],
      companyManagers: [],
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    excludedManagerIds() {
      return this.announcementManagers.map((manager) => manager.id);
    },
  },
  async created() {
    await this.loadAnnouncementManagers();
    await this.loadCompanyManagers(null);
  },
  methods: {
    async loadCompanyManagers(searchFilter) {
      try {
        const { data, error } = await this.$store.dispatch("announcementModule/loadAllManagers", searchFilter);
        if (data) {
          this.companyManagers = data.users;
        }
      } catch (error) {
        console.error(error);
      }
    },
    async loadAnnouncementManagers() {
      this.loadingAnnouncementManagers = true;
      try {
        const { data, error } = await this.$store.dispatch(
          "announcementModule/loadAnnouncementManagers",
          this.announcement.id
        );
        if (data) {
          this.announcementManagers = data;
        }
      } catch (error) {
        console.error(error);
      } finally {
       this.loadingAnnouncementManagers = false;
      }
    },
    async handleManagerRemoval(managerId) {
      this.loadingAnnouncementManagers = true;
      try {
        const { data, error } = await this.$store.dispatch(
          "announcementModule/removeAnnouncementManager",
          {
            announcementId: this.announcement.id,
            managerId: managerId
          }
        );
        if (data) {
          this.$toast.success(this.$t('ANNOUNCEMENT.SHARETAB.REMOVE.SUCCESS'));
          await this.loadAnnouncementManagers();
        }
      } catch (error) {
        console.error('Failed to remove manager:', error);
        this.$toast.error(this.$t('ANNOUNCEMENT.SHARETAB.REMOVE.FAILURE'));
      } finally {
        this.loadingAnnouncementManagers = false;
      }
    }
  },
};
</script>

<style scoped lang="scss">
.AnnouncementShare {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;

  .heading {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .manager-table {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      th,
      td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      th.auto-width,
      td.auto-width {
        width: 100px;
        white-space: nowrap;
        text-align: end;
      }

      th {
        font-weight: bold;
      }
    }

    .user-email {
      color: var(--color-primary);
    }
  }

  @media (max-width: 576px) {
    margin: 0 auto;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}
</style>
