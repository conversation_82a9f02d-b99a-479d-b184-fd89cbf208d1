<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Tests\Mother\Entity\UserMother;
use App\V2\Application\Query\Admin\GetRoles;
use App\V2\Application\QueryHandler\Admin\GetRolesHandler;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class GetRolesHandlerTest extends TestCase
{
    private GetRolesHandler $handler;

    protected function setUp(): void
    {
        $this->handler = new GetRolesHandler();
    }

    #[DataProvider('testHandlerUserRolesProvider')]
    public function testHandlerUserRoles(GetRoles $query, array $expectedRoles)
    {
        $result = $this->handler->handle($query);
        $this->assertEquals($expectedRoles, $result);
    }

    public static function testHandlerUserRolesProvider(): \Generator
    {
        yield 'super admin' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_SUPER_ADMIN']),
                allowedToManage : true
            ),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'admin' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_ADMIN']),
                allowedToManage : true
            ),
            'expectedRoles' => ['ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'manager' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_MANAGER']),
                allowedToManage : true
            ),
            'expectedRoles' => ['ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'creator' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_CREATOR']),
                allowedToManage : true
            ),
            'expectedRoles' => [],
        ];
        yield 'user' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_USER']),
                allowedToManage : true
            ),
            'expectedRoles' => [],
        ];
        yield 'non_allow_manage' => [
            'query' => new GetRoles(),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'super admin without allowed to manage' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_SUPER_ADMIN']),
                allowedToManage : false
            ),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'admin without allowed to manage' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_ADMIN']),
                allowedToManage : false
            ),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'manager without allowed to manage' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_MANAGER']),
                allowedToManage : false
            ),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'creator without allowed to manage' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_CREATOR']),
                allowedToManage : false
            ),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
        yield 'user without allowed to manage' => [
            'query' => new GetRoles(
                requestUser : UserMother::create(roles: ['ROLE_USER']),
                allowedToManage : false
            ),
            'expectedRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_TUTOR', 'ROLE_MANAGER', 'ROLE_CREATOR', 'ROLE_USER'],
        ];
    }
}
