<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class AdminAnnouncementEndpoints
{
    public static function getAnnouncementsEndpoint(int $page = 1): string
    {
        return '/announcements/' . $page;
    }

    public static function availableStudentsEndpoint(
        int $announcementId,
        ?int $page = null,
        ?int $pageSize = null
    ): string {
        $queryParams = [
            'announcement' => $announcementId,
        ];

        if (null !== $page) {
            $queryParams['page'] = $page;
        }

        if (null !== $pageSize) {
            $queryParams['page-size'] = $pageSize;
        }

        $url = '/announcement/form/available-students';

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    public static function impersonateUser(int $announcementId): string
    {
        return "/announcement/impersonate/{$announcementId}";
    }

    public static function getAnnouncementEndPoint(int $announcementId): string
    {
        return "/announcement/$announcementId";
    }

    public static function getAnnouncementManagersEndpoint(int $announcementId): string
    {
        return "/api/v2/admin/announcements/$announcementId/managers";
    }

    public static function announcementManagerEndpoint(int $announcementId, int $userId): string
    {
        return "/api/v2/admin/announcements/$announcementId/managers/$userId";
    }

    public static function announcementParticipantReportsEndpoint(): string
    {
        return '/announcement/reports/participants';
    }
}
