<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Resource;

use App\V2\Domain\Shared\Resource\ResourceType;

class ResourceTypeTransformer
{
    public static function toString(ResourceType $resourceType): string
    {
        return match ($resourceType) {
            ResourceType::Course => 'course',
        };
    }

    public static function fromString(string $resourceType): ResourceType
    {
        return match ($resourceType) {
            'course' => ResourceType::Course,
        };
    }
}
