PHP_CONTAINER=easylearning-backend-php
USER_ID:=$(shell id -u)
GROUP_ID:=$(shell id -g)

start:
	UID=${USER_ID} GID=${GROUP_ID} docker compose up --build -d --remove-orphans

stop:
	UID=${USER_ID} GID=${GROUP_ID} docker compose stop

destroy:
	UID=${USER_ID} GID=${GROUP_ID} docker compose down --remove-orphans

rebuild:
	UID=${USER_ID} GID=${GROUP_ID} docker compose build --pull --force-rm --no-cache
	make install
	make start

restart:
	make stop
	make start

install:
	make stop
	make start
	make composer-install
	make databasediff-force
	make catalogues

composer-install:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer install

composer-update:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer update

composer-require:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer require $(filter-out $@,$(<PERSON><PERSON>CMDG<PERSON>LS))

composer-req:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer require $(filter-out $@,$(MAKECMDGOALS))

composer-require-dev:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer require --dev $(filter-out $@,$(MAKECMDGOALS))

fix-code-project:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} bash -c "vendor/bin/php-cs-fixer fix --allow-risky=yes --config=.php-cs-fixer.php"

fix-code-staged:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} bash -c "git status -s | grep -e '^[ AM]' | cut -c4- | tr '\n' ' ' | xargs -r vendor/bin/php-cs-fixer --verbose --config=.php-cs-fixer.php fix --allow-risky=yes"

test:
	make clean-test-database
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console doctrine:migrations:migrate --all-or-nothing --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console catalogs:update --new --no-interaction --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} ./vendor/bin/phpunit

test-single:
	make clean-test-database
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console doctrine:migrations:migrate --all-or-nothing --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console catalogs:update --new --no-interaction --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} ./vendor/bin/phpunit --filter $(filter-out $@,$(MAKECMDGOALS))

clean-cache:
	@rm -rf var/cache/*
	@if [ -d vendor ]; then UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} ./bin/console cache:warmup; fi

bash:
	UID=${USER_ID} GID=${GROUP_ID} docker exec -it ${PHP_CONTAINER} bash

console:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} symfony console  $(filter-out $@,$(MAKECMDGOALS))

databasediff:
	UID=${USER_ID} GID=${GROUP_ID} docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete

databasediff-force:
	docker exec easylearning-backend-php symfony console doctrine:schema:update --force --complete

# Comandos alternativos sin UID/GID para Windows
databasediff-win:
	docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete

console-win:
	docker exec easylearning-backend-php symfony console $(filter-out $@,$(MAKECMDGOALS))

schema-validate:
	docker exec easylearning-backend-php symfony console doctrine:schema:validate

migrations-status:
	docker exec easylearning-backend-php symfony console doctrine:migrations:status

migrations-list:
	docker exec easylearning-backend-php symfony console doctrine:migrations:list

migrations-diff:
	docker exec easylearning-backend-php symfony console doctrine:migrations:diff

# Comandos de conexión y diagnóstico
test-db-connection:
	@echo "🔌 Probando conexión con la base de datos..."
	@docker exec easylearning-backend-php symfony console doctrine:query:sql "SELECT 'Conexión exitosa' as status, DATABASE() as database_name, NOW() as timestamp" || echo "❌ Error de conexión a la base de datos"

test-db-direct:
	@echo "🔌 Probando conexión directa a MariaDB..."
	@docker exec easylearning-database mysql -u root -pdocker -e "SELECT 'Conexión directa exitosa' as status, DATABASE() as current_db;" || echo "❌ Error de conexión directa"

test-containers:
	@echo "🐳 Verificando estado de contenedores..."
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep easylearning || echo "❌ No se encontraron contenedores easylearning"

test-network:
	@echo "🌐 Verificando conectividad de red entre contenedores..."
	@docker exec easylearning-backend-php ping -c 2 easylearning-database || echo "❌ No hay conectividad entre contenedores"

check-env:
	@echo "🔧 Verificando variables de entorno..."
	@docker exec easylearning-backend-php printenv | grep DATABASE_URL || echo "❌ DATABASE_URL no encontrada"

diagnose:
	@echo "🔍 Ejecutando diagnóstico completo..."
	@echo "1. Verificando contenedores..."
	@make test-containers
	@echo ""
	@echo "2. Verificando red..."
	@make test-network
	@echo ""
	@echo "3. Verificando variables de entorno..."
	@make check-env
	@echo ""
	@echo "4. Probando conexión directa..."
	@make test-db-direct
	@echo ""
	@echo "5. Probando conexión desde PHP..."
	@make test-db-connection
	@echo ""
	@echo "6. Verificando esquema..."
	@make schema-validate || echo "❌ Error en validación de esquema"

fix-connection:
	@echo "🔧 Intentando solucionar problemas de conexión..."
	@echo "1. Reiniciando contenedores..."
	docker-compose restart
	@echo "2. Esperando que los servicios estén listos..."
	@sleep 10
	@echo "3. Probando conexión..."
	@make test-db-connection

setup-docker-db:
	@echo "🔧 Configurando DATABASE_URL para uso con Docker..."
	@echo "DATABASE_URL=mysql://root:docker@easylearning-database:3306/easylearning" > .env.local.docker
	@echo "✅ Configuración Docker guardada en .env.local.docker"
	@echo "💡 Para usar: cp .env.local.docker .env.local"

setup-local-db:
	@echo "🔧 Configurando DATABASE_URL para uso local..."
	@echo "DATABASE_URL=mysql://root:docker@127.0.0.1:3316/easylearning" > .env.local.host
	@echo "✅ Configuración local guardada en .env.local.host"
	@echo "💡 Para usar: cp .env.local.host .env.local"

show-db-config:
	@echo "📋 Configuración actual de base de datos:"
	@grep "DATABASE_URL" .env.local || echo "❌ DATABASE_URL no encontrada en .env.local"
	@echo ""
	@echo "📋 Configuración base (.env):"
	@grep "DATABASE_URL" .env || echo "❌ DATABASE_URL no encontrada en .env"

# Comandos de análisis completo (Doctrine + V2)
analyze-structure:
	docker exec easylearning-backend-php symfony console app:database:compare-structure --include-v2

analyze-structure-v2-only:
	docker exec easylearning-backend-php symfony console app:database:compare-structure --v2-only

analyze-structure-client:
	docker exec easylearning-backend-php symfony console app:database:compare-structure $(CLIENT_DSN) --include-v2

analyze-structure-report:
	docker exec easylearning-backend-php symfony console app:database:compare-structure --include-v2 -o ./scripts/db-comparison/structure-report-$(shell date +%Y%m%d_%H%M%S).txt

clean-test-database:
	docker exec -i easylearning-database mysql -u root -pdocker -e "DROP DATABASE IF EXISTS easylearning_test; CREATE DATABASE IF NOT EXISTS easylearning_test;"

catalogues:
	docker exec easylearning-backend-php symfony console catalogs:update

catalogues-new:
	docker exec easylearning-backend-php symfony console catalogs:update --new

locales:
	docker exec easylearning-backend-php symfony console translation:pull --force --format=yaml

set-diploma:
	docker exec easylearning-backend-php symfony console app:set-diploma

deploy:
	node scripts/deploy.js

# Handle arguments after the target
%:
	@:
