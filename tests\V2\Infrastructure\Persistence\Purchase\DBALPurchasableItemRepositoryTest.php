<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Purchase;

use App\Tests\V2\Domain\Purchase\PurchasableItemRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Infrastructure\Persistence\Purchase\DBALPurchasableItemRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALPurchasableItemRepositoryTest extends PurchasableItemRepositoryTestCase
{
    private const string TABLE_NAME = 'purchasable_item';
    private Connection $connection;

    /**
     * @throws Exception
     * @throws SchemaException
     */
    protected function getRepository(): PurchasableItemRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALPurchasableItemRepository(
            $this->connection,
            self::TABLE_NAME
        );
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string', ['length' => 36]);
        $table->addColumn('name', 'string', ['length' => 255]);
        $table->addColumn('description', 'text');
        $table->addColumn('price_amount', 'integer');
        $table->addColumn('price_currency', 'string', ['length' => 3]);
        $table->addColumn('resource_type', 'string', ['length' => 50]);
        $table->addColumn('resource_id', 'string', ['length' => 36]);
        $table->addColumn('created_at', 'datetime');
        $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
        $table->addColumn('deleted_at', 'datetime', ['notnull' => false]);
        $table->setPrimaryKey(['id']);
        $table->addUniqueIndex(['resource_type', 'resource_id'], 'unique_resource');

        $this->connection->createSchemaManager()->createTable($table);
    }
}
