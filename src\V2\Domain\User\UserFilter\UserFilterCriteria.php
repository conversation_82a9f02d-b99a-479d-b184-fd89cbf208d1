<?php

declare(strict_types=1);

namespace App\V2\Domain\User\UserFilter;

use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Id\Id;

/**
 * @extends Criteria<UserFilterCriteria>
 */
class UserFilterCriteria extends Criteria
{
    private ?Id $userId = null;
    private ?Id $filterId = null;

    public function isEmpty(): bool
    {
        return null === $this->userId && null === $this->filterId;
    }

    public function filterByUserId(Id $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function filterByFilterId(Id $filterId): self
    {
        $this->filterId = $filterId;

        return $this;
    }

    public function getUserId(): ?Id
    {
        return $this->userId;
    }

    public function getFilterId(): ?Id
    {
        return $this->filterId;
    }
}
