#!/bin/bash

# Script de diagnóstico para verificar el entorno

echo "=== DIAGNÓSTICO DEL ENTORNO ==="
echo "Fecha: $(date)"
echo ""

# Verificar Docker
echo "1. VERIFICANDO DOCKER..."
if command -v docker &> /dev/null; then
    echo "✅ Docker instalado: $(docker --version)"
    
    # Verificar contenedores
    echo ""
    echo "Contenedores corriendo:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    # Verificar si el contenedor PHP existe
    if docker ps --format "{{.Names}}" | grep -q "easylearning-backend-php"; then
        echo ""
        echo "2. VERIFICANDO CONTENEDOR PHP..."
        echo "✅ Contenedor PHP corriendo"
        
        # Verificar PHP en contenedor
        echo "Versión PHP en contenedor:"
        docker exec easylearning-backend-php php --version
        
        # Verificar Composer
        echo ""
        echo "Versión Composer en contenedor:"
        docker exec easylearning-backend-php composer --version
        
        # Verificar directorio vendor
        echo ""
        echo "Verificando directorio vendor:"
        if docker exec easylearning-backend-php test -d /var/www/html/vendor; then
            echo "✅ Directorio vendor existe"
            
            # Verificar tactician
            if docker exec easylearning-backend-php test -d /var/www/html/vendor/league/tactician-bundle; then
                echo "✅ TacticianBundle instalado"
            else
                echo "❌ TacticianBundle NO encontrado"
                echo "Ejecuta: docker exec easylearning-backend-php composer install"
            fi
        else
            echo "❌ Directorio vendor NO existe"
            echo "Ejecuta: docker exec easylearning-backend-php composer install"
        fi
        
        # Verificar autoload
        echo ""
        echo "Verificando autoload:"
        if docker exec easylearning-backend-php test -f /var/www/html/vendor/autoload.php; then
            echo "✅ Autoload existe"
        else
            echo "❌ Autoload NO existe"
        fi
        
    else
        echo "❌ Contenedor PHP NO está corriendo"
        echo "Ejecuta: docker-compose up -d"
    fi
    
else
    echo "❌ Docker no está instalado"
fi

echo ""
echo "3. VERIFICANDO PHP LOCAL..."
if command -v php &> /dev/null; then
    echo "✅ PHP local instalado: $(php --version | head -n1)"
    
    # Verificar Composer local
    if command -v composer &> /dev/null; then
        echo "✅ Composer local instalado: $(composer --version)"
        
        # Verificar vendor local
        if [ -d "vendor" ]; then
            echo "✅ Directorio vendor local existe"
            
            if [ -d "vendor/league/tactician-bundle" ]; then
                echo "✅ TacticianBundle local instalado"
            else
                echo "❌ TacticianBundle local NO encontrado"
                echo "Ejecuta: composer install"
            fi
        else
            echo "❌ Directorio vendor local NO existe"
            echo "Ejecuta: composer install"
        fi
    else
        echo "❌ Composer local no está instalado"
    fi
else
    echo "❌ PHP local no está instalado"
fi

echo ""
echo "4. VERIFICANDO ARCHIVOS DE CONFIGURACIÓN..."

# Verificar .env.local
if [ -f ".env.local" ]; then
    echo "✅ .env.local existe"
    echo "Variables UID/GID:"
    grep -E "^(UID|GID|USER_ID|GROUP_ID)=" .env.local || echo "No se encontraron variables UID/GID"
else
    echo "❌ .env.local NO existe"
fi

# Verificar docker-compose.override.yml
if [ -f "docker-compose.override.yml" ]; then
    echo "✅ docker-compose.override.yml existe"
else
    echo "⚠️ docker-compose.override.yml NO existe"
fi

echo ""
echo "5. RECOMENDACIONES..."

# Verificar si hay problemas y dar recomendaciones
if ! docker ps --format "{{.Names}}" | grep -q "easylearning-backend-php"; then
    echo "🔧 Para usar Docker:"
    echo "   1. docker-compose up -d"
    echo "   2. docker exec easylearning-backend-php composer install"
    echo "   3. make databasediff-win"
fi

if command -v php &> /dev/null && [ -d "vendor" ]; then
    echo "🔧 Para usar PHP local:"
    echo "   1. docker-compose -f docker-compose.simple.yml up -d"
    echo "   2. php bin/console doctrine:schema:update --dump-sql --complete"
fi

echo ""
echo "=== FIN DEL DIAGNÓSTICO ==="
