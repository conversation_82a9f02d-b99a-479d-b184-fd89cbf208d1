<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Shared\Id\Id;

trait AnnouncementManagerFixtureTrait
{
    /**
     * Interacting with the InMemory repository.
     */
    private function setAndGetAnnouncementManger(
        ?int $userId = null,
        ?int $announcementId = null,
    ): AnnouncementManager {
        $manager = AnnouncementManagerMother::create(
            userId: new Id($userId),
            announcementId: new Id($announcementId),
        );

        $this->client->getContainer()->get('App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository')->insert($manager);

        return $manager;
    }
}
