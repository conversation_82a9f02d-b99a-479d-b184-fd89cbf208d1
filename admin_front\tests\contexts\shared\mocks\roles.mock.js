import { LISTS_API_ROUTES } from '@/contexts/shared/constants/lists.constants.js'
import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export default [
  {
    url: LISTS_API_ROUTES.ROLES,
    callback: () => {
      return {
        data: Object.values(USER_ROLE_LIST).map((role) => ({ key: role, name: role })),
        status: 200,
        error: false,
      }
    },
  },
]
