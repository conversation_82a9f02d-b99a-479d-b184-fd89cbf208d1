<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use PHPUnit\Framework\Attributes\DataProvider;

class CourseCreateFunctionalTest extends FunctionalTestCase
{
    private ?User $testUser = null;
    protected const string TEST_EMAIL = '<EMAIL>';

    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser = $this->createAndGetUser(
            firstName: 'test',
            lastName: 'test',
            roles: [User::ROLE_SUPER_ADMIN],
            email: self::TEST_EMAIL,
        );
    }

    #[DataProvider('createCourseCreatorProvider')]
    public function testIsCreatorCourseCreate(string $role, int $expectedStatusCode): void
    {
        $em = $this->getEntityManager();
        $em->refresh($this->testUser);
        $this->testUser->setRoles([$role]);
        $userToken = $this->loginAndGetToken(
            email: self::TEST_EMAIL,
        );
        $courseData = [
            'locale' => 'ES',
            'code' => '123456',
            'name' => 'Curso de prueba',
            'duration' => 120,
            'typeCourse' => 1,
            'category' => 1,
        ];

        $request = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::adminCourseEndpoint(),
            queryParams: $courseData,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $request->getStatusCode());
    }

    public static function createCourseCreatorProvider(): \Generator
    {
        yield 'Super Admin Role Course Creation' => [
            'role' => User::ROLE_SUPER_ADMIN,
            'expectedStatusCode' => 201,
        ];
        yield 'Admin Role Course Creation' => [
            'role' => User::ROLE_ADMIN,
            'expectedStatusCode' => 201,
        ];
        yield 'Creator Role Course Creation' => [
            'role' => User::ROLE_CREATOR,
            'expectedStatusCode' => 201,
        ];
        yield 'Tutor Role Course Creation' => [
            'role' => User::ROLE_TUTOR,
            'expectedStatusCode' => 403,
        ];
        yield 'Manager Role Course Creation' => [
            'role' => User::ROLE_MANAGER,
            'expectedStatusCode' => 403,
        ];
        yield 'User Role Course Creation' => [
            'role' => User::ROLE_USER,
            'expectedStatusCode' => 302,
        ];
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            UserLogin::class,
            Course::class,
            CourseCategory::class,
            TypeCourse::class,
        ]);
        // Hard delete user created for the test
        if (!empty($this->testUser)) {
            $this->hardDeleteUsersByIds([
                $this->testUser->getId(),
            ]);
        }
        parent::tearDown();
    }
}
