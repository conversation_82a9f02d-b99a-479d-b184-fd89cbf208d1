<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\CreateCourseValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class CreateCourseValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('createCourseValidatorSuccessProvider')]
    public function testCreateCourseValidatorSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        CreateCourseValidator::validateCreateCourseRequest($payload);
    }

    public static function createCourseValidatorSuccessProvider(): \Generator
    {
        yield 'valid name, code, locale, duration, category and typeCourse' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
        ];
    }

    #[DataProvider('createCourseValidatorFailProvider')]
    public function testCreateCourseValidatorFail(array $payload, array $violations): void
    {
        try {
            CreateCourseValidator::validateCreateCourseRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function createCourseValidatorFailProvider(): \Generator
    {
        yield 'missing name' => [
            'payload' => [
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[name]' => 'This field is missing.',
            ],
        ];
        yield 'wrong name value' => [
            'payload' => [
                'name' => 123,
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
            ],
        ];
        yield 'missing code' => [
            'payload' => [
                'name' => 'Test Course',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[code]' => 'This field is missing.',
            ],
        ];
        yield 'wrong code value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 123123,
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[code]' => 'This value should be of type string.',
            ],
        ];
        yield 'missing locale' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 123,
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[locale]' => 'This value should be of type string.',
            ],
        ];
        yield 'missing duration' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[duration]' => 'This field is missing.',
            ],
        ];
        yield 'wrong duration value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 'invalid',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[duration]' => 'This value must be null or numeric.',
            ],
        ];
        yield 'missing category' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[category]' => 'This field is missing.',
            ],
        ];
        yield 'wrong category value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 'invalid',
                'typeCourse' => 1,
            ],
            'violations' => [
                '[category]' => 'This value should be of type numeric.',
            ],
        ];
        yield 'missing typeCourse' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
            ],
            'violations' => [
                '[typeCourse]' => 'This field is missing.',
            ],
        ];
        yield 'wrong typeCourse value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 'invalid',
            ],
            'violations' => [
                '[typeCourse]' => 'This value should be of type numeric.',
            ],
        ];
    }
}
