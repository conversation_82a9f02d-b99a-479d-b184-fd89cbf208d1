<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Announcement\Manager;

use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepositoryException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class AnnouncementManagerRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): AnnouncementManagerRepository;

    /**
     * @throws InfrastructureException
     */
    public function testInsert(): void
    {
        $repository = $this->getRepository();

        $announcementManager1 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));
        $announcementManager2 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(2));
        $announcementManager3 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(1));
        $announcementManager4 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(2));

        $repository->insert($announcementManager1);
        $repository->insert($announcementManager2);
        $repository->insert($announcementManager3);
        $repository->insert($announcementManager4);

        // Test duplicate insertion - repeat the first scenario (user: 1, announcement: 1)
        $duplicateAnnouncementManager = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));

        try {
            $repository->insert($duplicateAnnouncementManager);
            $this->fail('Expected exception was not thrown');
        } catch (AnnouncementManagerRepositoryException $e) {
            $this->assertEquals(
                AnnouncementManagerRepositoryException::duplicateAnnouncementManager($duplicateAnnouncementManager),
                $e
            );
        }
    }

    /**
     * @throws InfrastructureException
     * @throws AnnouncementManagerNotFoundException
     */
    public function testFindOneBy(): void
    {
        $announcementManager1 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));
        $announcementManager2 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(2));
        $announcementManager3 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(3));
        $announcementManager4 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(4));
        $announcementManager5 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(5));

        $repository = $this->getRepository();
        $repository->insert($announcementManager1);
        $repository->insert($announcementManager2);
        $repository->insert($announcementManager3);
        $repository->insert($announcementManager4);
        $repository->insert($announcementManager5);

        $found = $repository->findOneBy(
            AnnouncementManagerCriteria::createEmpty()->filterByUserId(2)
        );
        $this->assertEquals($announcementManager2, $found);

        $found = $repository->findOneBy(
            AnnouncementManagerCriteria::createEmpty()
                ->filterByAnnouncementId(2)
        );
        $this->assertEquals($announcementManager2, $found);

        $found = $repository->findOneBy(
            AnnouncementManagerCriteria::createEmpty()
                ->filterByAnnouncementId(4)
        );
        $this->assertEquals($announcementManager4, $found);

        $found = $repository->findOneBy(
            AnnouncementManagerCriteria::createEmpty()
        );
        $this->assertEquals($announcementManager1, $found);

        $found = $repository->findOneBy(
            AnnouncementManagerCriteria::createEmpty()
                ->filterByUserId(1)
                ->filterByAnnouncementId(5)
        );
        $this->assertEquals($announcementManager5, $found);
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideAnnouncementManagerNotFoundException')]
    public function testFindOneByAnnouncementManagerNotFoundException(AnnouncementManagerCriteria $criteria): void
    {
        $repository = $this->getRepository();

        $this->expectException(AnnouncementManagerNotFoundException::class);
        $repository->findOneBy($criteria);
    }

    public static function provideAnnouncementManagerNotFoundException(): \Generator
    {
        yield 'not found by user id' => [
            'criteria' => AnnouncementManagerCriteria::createEmpty()->filterByUserId(-1),
        ];

        yield 'not found by announcement id' => [
            'criteria' => AnnouncementManagerCriteria::createEmpty()->filterByAnnouncementId(-1),
        ];

        yield 'not found by user id and announcement id' => [
            'criteria' => AnnouncementManagerCriteria::createEmpty()
                ->filterByUserId(-1)
                ->filterByAnnouncementId(-1),
        ];
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        array $elements,
        AnnouncementManagerCriteria $criteria,
        int $expectedCount,
        array $expectedResult,
    ): void {
        $repository = $this->getRepository();
        foreach ($elements as $element) {
            $repository->insert($element);
        }

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);
        $difference = array_diff(
            $expectedResult,
            $result->all()
        );
        $this->assertCount(0, $difference);
    }

    public static function provideFindBy(): \Generator
    {
        $announcementManager1 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));
        $announcementManager2 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(2));
        $announcementManager3 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(3));
        $announcementManager4 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(4));

        yield '4 announcement managers find all' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'criteria' => AnnouncementManagerCriteria::createEmpty(),
            'expectedCount' => 4,
            'expectedResult' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
        ];

        yield '4 announcement managers find two user number 2' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'criteria' => AnnouncementManagerCriteria::createEmpty()->filterByUserId(2),
            'expectedCount' => 2,
            'expectedResult' => [$announcementManager2, $announcementManager3],
        ];

        yield '4 announcement managers find two user number 1' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'criteria' => AnnouncementManagerCriteria::createEmpty()->filterByUserId(1),
            'expectedCount' => 2,
            'expectedResult' => [$announcementManager1, $announcementManager4],
        ];

        yield '4 announcement managers find one announcement number 1' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'criteria' => AnnouncementManagerCriteria::createEmpty()
                ->filterByAnnouncementId(1),
            'expectedCount' => 1,
            'expectedResult' => [$announcementManager1],
        ];

        yield '4 announcement managers find one announcement number 3' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'criteria' => AnnouncementManagerCriteria::createEmpty()
                ->filterByAnnouncementId(3),
            'expectedCount' => 1,
            'expectedResult' => [$announcementManager3],
        ];
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideDelete')]
    public function testDelete(
        array $elements,
        array $toDelete,
        array $expectedResult,
    ): void {
        $repository = $this->getRepository();
        foreach ($elements as $element) {
            $repository->insert($element);
        }

        $result = $repository->findBy(AnnouncementManagerCriteria::createEmpty());
        $this->assertCount(
            0,
            array_diff(
                $elements,
                $result->all(),
            )
        );

        foreach ($toDelete as $element) {
            $repository->delete($element);
        }

        $result = $repository->findBy(AnnouncementManagerCriteria::createEmpty());
        $difference = array_diff(
            $expectedResult,
            $result->all()
        );
        $this->assertCount(0, $difference);
    }

    public static function provideDelete(): \Generator
    {
        $announcementManager1 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(1));
        $announcementManager2 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(2));
        $announcementManager3 = AnnouncementManagerMother::create(userId: new Id(2), announcementId: new Id(3));
        $announcementManager4 = AnnouncementManagerMother::create(userId: new Id(1), announcementId: new Id(4));

        yield '4 announcement managers delete 3' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'toDelete' => [$announcementManager3],
            'expectedResult' => [$announcementManager1, $announcementManager2, $announcementManager4],
        ];

        yield '4 announcement managers delete 2' => [
            'elements' => [$announcementManager1, $announcementManager2, $announcementManager3, $announcementManager4],
            'toDelete' => [$announcementManager2, AnnouncementManagerMother::create(userId: new Id(999), announcementId: new Id(999))],
            'expectedResult' => [$announcementManager1, $announcementManager3, $announcementManager4],
        ];
    }
}
