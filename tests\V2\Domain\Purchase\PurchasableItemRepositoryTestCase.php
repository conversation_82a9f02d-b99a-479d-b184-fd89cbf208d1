<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\Tests\V2\Mother\Purchase\PurchasableItemMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class PurchasableItemRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): PurchasableItemRepository;

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testPut(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createById($item1->getId())
        );
        $this->assertEquals($item1, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createById($item2->getId())
        );
        $this->assertEquals($item2, $found);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     */
    public function testFindOneBy(): void
    {
        $resource1 = ResourceMother::create(id: new Id(1));
        $resource2 = ResourceMother::create(id: new Id(2));
        $resource3 = ResourceMother::create(id: new Id(3));
        $price1 = MoneyMother::create(amount: 1000);
        $price2 = MoneyMother::create(amount: 2000);

        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: $price1,
            resource: $resource1
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: $price2,
            resource: $resource2
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            price: $price1,
            resource: $resource3
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $this->assertEquals(
            $item2,
            $repository->findOneBy(
                PurchasableItemCriteria::createEmpty()
                    ->filterById($item2->getId())
            )
        );

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByResource($resource2)
        );
        $this->assertEquals($item2, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice($price2)
        );
        $this->assertEquals($item2, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterByMaxPrice($price1)
        );
        $this->assertTrue(
            $found->getId()->equals($item1->getId())
            || $found->getId()->equals($item3->getId()),
            'Found item should be either item1 or item3'
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testFindOneByThrowsExceptionWhenNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(PurchasableItemNotFoundException::class);
        $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById(UuidMother::create())
        );
    }

    #[DataProvider('provideFindBy')]
    public function testFindBy(
        PurchasableItemCollection $input,
        PurchasableItemCriteria $criteria,
        int $expectedCount,
        array $expectedResults,
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $item) {
            $repository->put($item);
        }

        $result = $repository->findBy(
            PurchasableItemCriteria::createEmpty()
        );
        $this->assertCount($input->count(), $result);

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);

        $expectedResultsIds = array_map(
            fn ($item) => $item->getId(),
            $expectedResults
        );

        $foundIds = array_map(
            fn ($item) => $item->getId(),
            $result->all()
        );

        $this->assertCount(
            0,
            array_diff($expectedResultsIds, $foundIds),
            'Expected results not found in results'
        );
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public static function provideFindBy(): \Generator
    {
        $resource1 = ResourceMother::create(id: new Id(1));
        $resource2 = ResourceMother::create(id: new Id(2));
        $resource3 = ResourceMother::create(id: new Id(3));
        $price1 = MoneyMother::create(amount: 1000);
        $price2 = MoneyMother::create(amount: 2000);
        $price3 = MoneyMother::create(amount: 3000);

        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            price: $price1,
            resource: $resource1
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            price: $price2,
            resource: $resource2
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            price: $price3,
            resource: $resource3
        );

        yield '3 items get all' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$item1, $item2, $item3],
        ];

        yield '3 items get item 2 by id' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterById($item2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$item2],
        ];

        yield '3 items get by resource 1' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByResource($resource1),
            'expectedCount' => 1,
            'expectedResults' => [$item1],
        ];

        yield '3 items get by min price 2000' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice($price2),
            'expectedCount' => 2,
            'expectedResults' => [$item2, $item3],
        ];

        yield '3 items get by max price 2000' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMaxPrice($price2),
            'expectedCount' => 2,
            'expectedResults' => [$item1, $item2],
        ];

        yield '3 items get by price range 1500-2500' => [
            'input' => new PurchasableItemCollection([$item1, $item2, $item3]),
            'criteria' => PurchasableItemCriteria::createEmpty()
                ->filterByMinPrice(MoneyMother::create(amount: 1500))
                ->filterByMaxPrice(MoneyMother::create(amount: 2500)),
            'expectedCount' => 1,
            'expectedResults' => [$item2],
        ];
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public function testDelete(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item2->getId())
        );
        $this->assertEquals($item2, $found);

        $repository->delete($item2);

        $this->expectException(PurchasableItemNotFoundException::class);
        $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item2->getId())
        );
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public function testDeleteDoesNotAffectOtherItems(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(id: new Id(1))
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(id: new Id(2))
        );
        $item3 = PurchasableItemMother::create(
            name: 'Item 3',
            resource: ResourceMother::create(id: new Id(3))
        );

        $repository = $this->getRepository();

        $repository->put($item1);
        $repository->put($item2);
        $repository->put($item3);

        $repository->delete($item2);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item1->getId())
        );
        $this->assertEquals($item1, $found);

        $found = $repository->findOneBy(
            PurchasableItemCriteria::createEmpty()
                ->filterById($item3->getId())
        );
        $this->assertEquals($item3, $found);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testEnsureUniqueResource(): void
    {
        $item1 = PurchasableItemMother::create(
            name: 'Item 1',
            resource: ResourceMother::create(
                type: ResourceType::Course,
                id: new Id(1)
            ),
        );
        $item2 = PurchasableItemMother::create(
            name: 'Item 2',
            resource: ResourceMother::create(
                type: ResourceType::Course,
                id: new Id(1)
            )
        );

        $this->expectException(PurchasableItemRepositoryException::class);
        $repository = $this->getRepository();
        $repository->put($item1);
        $repository->put($item2);
    }
}
