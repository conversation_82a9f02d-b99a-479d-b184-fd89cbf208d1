<?php

declare(strict_types=1);

namespace App\V2\Domain\User\ManagerFilter;

use App\V2\Domain\Shared\Entity\Entity;
use App\V2\Domain\Shared\Id\Id;

class ManagerFilter implements Entity
{
    public function __construct(
        private readonly Id $userId,
        private readonly Id $filterId,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getFilterId(): Id
    {
        return $this->filterId;
    }

    public function __toString(): string
    {
        return "{$this->userId}:{$this->filterId}";
    }
}
