<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Required;
use Symfony\Component\Validator\Constraints\Type;

class CreateCourseValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateCreateCourseRequest(array $data): void
    {
        $constraints = new Collection([
            'name' => [
                new NotBlank(),
                new Type('string'),
            ],
            'code' => [
                new NotBlank(),
                new Type('string'),
            ],
            'locale' => [
                new NotBlank(),
                new Type('string'),
            ],
            'duration' => new Required([
                new Callback(
                    function ($value, $context) {
                        if (null === $value || 'null' === $value) {
                            return;
                        }
                        if (is_numeric($value)) {
                            return;
                        }
                        $context->buildViolation('This value must be null or numeric.')
                            ->addViolation();
                    }
                ),
            ]),
            'category' => [
                new NotBlank(),
                new Type('numeric'),
            ],
            'typeCourse' => [
                new NotBlank(),
                new Type('numeric'),
            ],
        ]);
        parent::validate($data, $constraints);
    }
}
