get_admin_users:
  path: /users
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetUsersController

get_admin_creators:
  path: users/creators
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetCreatorsController

get_admin_managers:
  path: users/managers
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetManagersController

get_admin_course_creators:
  path: /courses/{courseId}/creators
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetCourseCreatorsController

put_admin_course_creators:
  path: /courses/{courseId}/creators/{userId}
  methods: PUT
  controller: App\V2\Infrastructure\Controller\Admin\PutCourseCreatorController

delete_admin_course_creator:
  path: courses/{courseId}/creators/{userId}
  methods: DELETE
  controller: App\V2\Infrastructure\Controller\Admin\DeleteCourseCreatorController

post_lti_registration:
  path: /lti/registrations
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiRegistrationController

post_lti_platform:
  path: /lti/registrations/{registrationId}/platform
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiPlatformController

post_lti_tool:
  path: /lti/registrations/{registrationId}/tool
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiToolController

post_lti_deployment:
  path: /lti/registrations/{registrationId}/deployments
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiDeploymentController

get_launch_lti_chapter:
  path: /courses/{courseId}/chapter/{chapterId}/lti-launch
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetLaunchLtiChapterController

get_announcement_managers:
  path: /announcements/{announcementId}/managers
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetAnnouncementManagersController

put_announcement_manager:
  path: /announcements/{announcementId}/managers/{userId}
  methods: PUT
  controller: App\V2\Infrastructure\Controller\Admin\PutAnnouncementManagerController

delete_announcement_manager:
  path: /announcements/{announcementId}/managers/{userId}
  methods: DELETE
  controller: App\V2\Infrastructure\Controller\Admin\DeleteAnnouncementManagerController

get_lti_registrations:
  path: /lti/registrations
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetLtiRegistrationsController

get_lti_registration:
  path: /lti/registrations/{registrationId}
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetLtiRegistrationController

get_roles:
  path: /roles
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetRolesController

get_admin_purchasable_items:
  path: /purchasable-items
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetPurchasableItemsController

get_admin_purchasable_item:
  path: /purchasable-items/{purchasableItemId}
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetPurchasableItemController

put_admin_purchasable_item:
  path: /purchasable-items
  methods: PUT
  controller: App\V2\Infrastructure\Controller\Admin\PutPurchasableItemController

patch_admin_purchasable_item:
  path: /purchasable-items/{purchasableItemId}
  methods: PATCH
  controller: App\V2\Infrastructure\Controller\Admin\PatchPurchasableItemController

delete_admin_purchasable_item:
  path: /purchasable-items/{purchasableItemId}
  methods: DELETE
  controller: App\V2\Infrastructure\Controller\Admin\DeletePurchasableItemController

get_admin_locales:
  path: /locales
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetLocalesController
