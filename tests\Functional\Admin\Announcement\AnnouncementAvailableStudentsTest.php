<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Filter\FilterCollection;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class AnnouncementAvailableStudentsTest extends FunctionalTestCase
{
    private const TIMEZONE_MADRID = 'Europe/Madrid';
    private const TIMEZONE_UTC = 'UTC';

    private ?Course $course = null;
    private ?User $user = null;
    private ?User $user1 = null;
    private ?User $user2 = null;
    private ?User $user3 = null;

    private $maxUsers = 0;
    private ?Announcement $announcementFundae = null;
    private ?Announcement $announcementNonFundae = null;
    private ?Announcement $announcementOverlapping = null;

    /**
     * @throws \DateMalformedStringException
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->createTestData();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException|\DateMalformedStringException
     */
    private function createTestData(): void
    {
        $em = $this->getEntityManager();
        $this->user = $this->getDefaultUser();

        // Create course
        $this->course = $this->createAndGetCourse(name: 'Test Course for Available Students');

        // Create users
        $this->user1 = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'One',
            email: '<EMAIL>',
        );

        $this->user2 = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'Two',
            email: '<EMAIL>',
        );

        $this->user3 = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'Three',
            email: '<EMAIL>',
        );

        $this->maxUsers = $this->getEntityManager()->getRepository(User::class)->count([]);

        // Create announcements with timezone
        $timezone = new \DateTimeZone(self::TIMEZONE_MADRID);

        // Fundae announcement: 2025-01-15 10:00 to 2025-01-15 18:00 (Madrid time)
        $this->announcementFundae = $this->createAndGetAnnouncement(
            course: $this->course,
            startAt: new \DateTimeImmutable('2025-01-15 10:00:00', $timezone),
            finishAt: new \DateTimeImmutable('2025-01-15 18:00:00', $timezone),
            subsidized: true,
            code: 'FUNDAE-TEST-001',
            timezone: self::TIMEZONE_MADRID
        );

        // Configure as Fundae announcement
        $this->configureFundaeAnnouncement($this->announcementFundae);

        // Non-Fundae announcement: 2025-01-20 09:00 to 2025-01-20 17:00 (Madrid time)
        $this->announcementNonFundae = $this->createAndGetAnnouncement(
            course: $this->course,
            startAt: new \DateTimeImmutable('2025-01-20 09:00:00', $timezone),
            finishAt: new \DateTimeImmutable('2025-01-20 17:00:00', $timezone),
            subsidized: false,
            code: 'NON-FUNDAE-TEST-001',
            timezone: self::TIMEZONE_MADRID
        );

        // Overlapping announcement: 2025-01-15 14:00 to 2025-01-15 22:00 (Madrid time)
        $this->announcementOverlapping = $this->createAndGetAnnouncement(
            course: $this->course,
            startAt: new \DateTimeImmutable('2025-01-15 14:00:00', $timezone),
            finishAt: new \DateTimeImmutable('2025-01-15 22:00:00', $timezone),
            code: 'OVERLAP-TEST-001',
            timezone: self::TIMEZONE_MADRID
        );

        // Add users to overlapping announcement to create conflicts
        $this->addUserToAnnouncement($this->user1, $this->announcementOverlapping);
        $this->addUserToAnnouncement($this->user2, $this->announcementOverlapping);

        $em->flush();
    }

    /**
     * @throws ORMException
     */
    private function addUserToAnnouncement(User $user, Announcement $announcement): void
    {
        $em = $this->getEntityManager();

        $announcementUser = new AnnouncementUser();
        $announcementUser->setUser($user);
        $announcementUser->setAnnouncement($announcement);

        $em->persist($announcementUser);
    }

    /**
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws ORMException
     */
    private function configureFundaeAnnouncement(Announcement $announcement): void
    {
        $em = $this->getEntityManager();

        // Set actionType to FUNDAE_TRIPARTITA
        $announcement->setActionType('FUNDAE_TRIPARTITA');

        // Get the subsidized course configuration type
        $configurationType = $em->getRepository(AnnouncementConfigurationType::class)
            ->find(AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE);

        if ($configurationType) {
            // Create the configuration to enable bonification
            $configuration = new AnnouncementConfiguration();
            $configuration->setAnnouncement($announcement);
            $configuration->setConfiguration($configurationType);

            $em->persist($configuration);
        }

        $em->flush();
    }

    /**
     * Test that Fundae announcements return all users with correct isDuplicated flag.
     */
    public function testFundaeAnnouncementReturnsAllUsersWithIsDuplicated(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::availableStudentsEndpoint(
                announcementId: $this->announcementFundae->getId(),
                page: 1,
                pageSize: 10
            ),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $data = $this->extractResponseData($response);

        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('totalItems', $data);

        $users = $data['data'];
        $this->assertGreaterThan(0, \count($users), 'Should return users');

        // Find our test users in the response
        $user1Data = $this->findUserInResponse($users, $this->user1->getId());
        $user2Data = $this->findUserInResponse($users, $this->user2->getId());
        $user3Data = $this->findUserInResponse($users, $this->user3->getId());

        // Users 1 and 2 should be marked as duplicated (they are in overlapping announcement)
        $this->assertNotNull($user1Data, 'User 1 should be in response');
        $this->assertNotNull($user2Data, 'User 2 should be in response');
        $this->assertNotNull($user3Data, 'User 3 should be in response');

        $this->assertTrue($user1Data['isDuplicated'], 'User 1 should be marked as duplicated');
        $this->assertTrue($user2Data['isDuplicated'], 'User 2 should be marked as duplicated');
        $this->assertFalse($user3Data['isDuplicated'], 'User 3 should not be marked as duplicated');
    }

    /**
     * Test that non-Fundae announcements return all users with correct isDuplicated flag.
     */
    public function testNonFundaeAnnouncementReturnsAllUsersWithIsDuplicated(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::availableStudentsEndpoint(
                announcementId: $this->announcementNonFundae->getId(),
                page: 1,
                pageSize: 10
            ),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $data = $this->extractResponseData($response);
        $users = $data['data'];

        // Find our test users in the response
        $user1Data = $this->findUserInResponse($users, $this->user1->getId());
        $user2Data = $this->findUserInResponse($users, $this->user2->getId());
        $user3Data = $this->findUserInResponse($users, $this->user3->getId());

        // All users should be present, none should be duplicated for this announcement
        $this->assertNotNull($user1Data, 'User 1 should be in response');
        $this->assertNotNull($user2Data, 'User 2 should be in response');
        $this->assertNotNull($user3Data, 'User 3 should be in response');

        $this->assertFalse($user1Data['isDuplicated'], 'User 1 should not be marked as duplicated');
        $this->assertFalse($user2Data['isDuplicated'], 'User 2 should not be marked as duplicated');
        $this->assertFalse($user3Data['isDuplicated'], 'User 3 should not be marked as duplicated');
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws CollectionException
     * @throws Exception
     */
    #[DataProvider('nonFundaeAnnouncementWithUserPermissionsDataProvider')]
    public function testNonFundaeAnnouncementWithUserPermissions(
        $userRoles,
        $userManagerFilters,
        $filters,
        $expectedResponseStatus = null,
        $expectedUsers = null
    ): void {
        $this->user->setRoles($userRoles);

        // Create Filters.
        $filterCategory1 = $this->createAndGetFilterCategory(name: 'Test Category 1');
        $filterCategory2 = $this->createAndGetFilterCategory(name: 'Test Category 2');
        $filterCategory3 = $this->createAndGetFilterCategory(name: 'Test Category 3');
        $filter1 = $this->createAndGetFilter(name: 'Test Filter 1', category: $filterCategory1);
        $filter2 = $this->createAndGetFilter(name: 'Test Filter 2', category: $filterCategory2);
        $filter3 = $this->createAndGetFilter(name: 'Test Filter 3', category: $filterCategory3);

        $filterCollection1 = new FilterCollection([
            $filter1,
            $filter2,
        ]);

        $filterCollection2 = new FilterCollection([$filter3]);

        $this->user1->setFilters($filterCollection1->all());
        $this->user2->setFilters($filterCollection1->all());
        $this->user3->setFilters($filterCollection2->all());

        // Add manager filters.
        $managerFilters = [];
        foreach ($userManagerFilters as $filterName) {
            $filter = $this->getEntityManager()->getRepository(Filter::class)->findOneBy(['name' => $filterName]);
            if ($filter) {
                $managerFilters[] = $filter;
                $this->user->addManagerFilter($filter);
            }
        }

        // Create search filters.
        $searchFilters = [];
        foreach ($filters as $filterName) {
            $filter = $this->getEntityManager()->getRepository(Filter::class)->findOneBy(['name' => $filterName]);
            if ($filter) {
                $filterCategory = $filter->getFilterCategory();
                if (!isset($searchFilters[$filterCategory->getId()])) {
                    $searchFilters[$filterCategory->getName()] = [];
                }
                $searchFilters[$filterCategory->getName()][] = [
                    'id' => $filter->getId(),
                    'name' => $filter->getName(),
                ];
            }
        }

        $this->announcementNonFundae->setCreatedBy($this->user);
        $this->announcementOverlapping->setCreatedBy($this->user);
        $this->getEntityManager()->flush();

        $token = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::availableStudentsEndpoint(
                announcementId: $this->announcementNonFundae->getId(),
                page: 1,
                pageSize: 10
            ),
            body: ['filters' => $searchFilters],
            bearerToken: $token
        );

        $this->assertEquals($expectedResponseStatus, $response->getStatusCode());

        $this->removeManagerFilters($managerFilters);
        $this->truncateEntities([
            Filter::class,
            FilterCategory::class,
        ]);

        if (Response::HTTP_OK !== $expectedResponseStatus) {
            return;
        }

        $data = $this->extractResponseData($response);
        $users = $data['data'];

        $this->assertCount($expectedUsers ?? $this->maxUsers, $users);

        $this->getEntityManager()->flush();
    }

    public static function nonFundaeAnnouncementWithUserPermissionsDataProvider(): \Generator
    {
        yield 'User with multiple roles. No manager filters. With two filters for search.' => [
            'userRoles' => [
                'ROLE_ADMIN',
                'ROLE_MANAGER',
                'ROLE_TUTOR',
                'ROLE_USER',
            ],
            'userManagerFilters' => [],
            'filters' => [
                'Test Filter 1',
                'Test Filter 2',
            ],
            'expectedResponseStatus' => Response::HTTP_OK,
            'expectedUsers' => 2,
        ];

        yield 'User with multiple roles. One manager filters. With no filters for search.' => [
            'userRoles' => [
                'ROLE_ADMIN',
                'ROLE_MANAGER',
                'ROLE_TUTOR',
                'ROLE_USER',
            ],
            'userManagerFilters' => [
                'Test Filter 1',
            ],
            'filters' => [],
            'expectedResponseStatus' => Response::HTTP_OK,
        ];

        yield 'User as manager. One manager filters. With no filters for search.' => [
            'userRoles' => ['ROLE_MANAGER'],
            'userManagerFilters' => [
                'Test Filter 1',
            ],
            'filters' => [],
            'expectedResponseStatus' => Response::HTTP_OK,
            'expectedUsers' => 2,
        ];

        yield 'User as manager. With three manager filters. With one filter for search that match a manager filter. ' => [
            'userRoles' => ['ROLE_MANAGER'],
            'userManagerFilters' => [
                'Test Filter 1',
                'Test Filter 2',
                'Test Filter 3',
            ],
            'filters' => [
                'Test Filter 3',
            ],
            'expectedResponseStatus' => Response::HTTP_OK,
            'expectedUsers' => 1,
        ];

        yield 'User as manager. With two manager filters. With one filter for search that does not match any manager filter. ' => [
            'userRoles' => ['ROLE_MANAGER'],
            'userManagerFilters' => [
                'Test Filter 1',
                'Test Filter 2',
            ],
            'filters' => [
                'Test Filter 3',
            ],
            'expectedResponseStatus' => Response::HTTP_FORBIDDEN,
        ];
    }

    /**
     * Test error handling scenarios.
     *
     * @dataProvider errorScenariosProvider
     */
    public function testErrorHandling(int $announcementId, int $expectedStatusCode, string $expectedErrorMessage, string $scenario): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::availableStudentsEndpoint(
                announcementId: $announcementId,
                page: 1,
                pageSize: 10
            ),
            bearerToken: $token
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['error']);
        $this->assertStringContainsString($expectedErrorMessage, $responseData['data']);
    }

    /**
     * Data provider for error scenarios.
     */
    public static function errorScenariosProvider(): array
    {
        return [
            'missing_announcement_id' => [0, Response::HTTP_BAD_REQUEST, 'The "announcement" parameter is required', 'missing_id'],
            'non_existent_announcement' => [99999, Response::HTTP_NOT_FOUND, 'Announcement not found', 'non_existent'],
        ];
    }

    private function findUserInResponse(array $users, int $userId): ?array
    {
        foreach ($users as $user) {
            if ($user['id'] === $userId) {
                return $user;
            }
        }

        return null;
    }

    /**
     * @throws Exception
     */
    private function removeManagerFilters(array $managerFilters): void
    {
        if (empty($managerFilters)) {
            return;
        }

        $managerFiltersId = array_map(fn ($filter) => $filter->getId(), $managerFilters);

        $em = $this->getEntityManager();
        $connection = $em->getConnection();

        try {
            // Disable foreign key checks
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS=0');

            $placeholders = implode(',', array_fill(0, \count($managerFiltersId), '?'));
            $connection->executeStatement(
                "DELETE FROM manager_filter WHERE filter_id IN ($placeholders)",
                $managerFiltersId
            );
        } finally {
            // Re-enable foreign key checks
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS=1');
        }
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            AnnouncementConfiguration::class,
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
        ]);

        // Hard delete only the users created for this test
        $userIds = array_filter([
            $this->user1?->getId(),
            $this->user2?->getId(),
            $this->user3?->getId(),
        ]);

        if (!empty($userIds)) {
            $this->hardDeleteUsersByIds($userIds);
        }

        parent::tearDown();
    }
}
