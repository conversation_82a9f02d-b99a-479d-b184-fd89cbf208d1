<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PutCourseCreator;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PutCourseCreatorController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     */
    public function __invoke(Request $request, int $courseId, int $userId): Response
    {
        IdValidator::validateId($courseId);
        IdValidator::validateId($userId);

        $user = $this->getUser();

        $this->execute(
            new PutCourseCreator(
                courseId: new Id($courseId),
                userId: new Id($userId),
                requestUser: $user,
            )
        );

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }
}
