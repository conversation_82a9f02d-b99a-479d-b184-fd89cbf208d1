<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement\Manager;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Entity\User;
use App\Enum\SettingGroupCode;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class DeleteAnnouncementManagerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use AnnouncementHelperTrait;
    use AnnouncementManagerFixtureTrait;
    use SettingHelperTrait;

    public const string MANAGER_SHARED_ANNOUNCEMENT_SETTING = 'app.announcement.managers.sharing';

    protected array $usersIds = [];

    /**
     * @throws NotSupported
     */
    protected function setUp(): void
    {
        parent::setUp();

        $setting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

        $this->originalSettingValue = $setting?->getValue();

        $this->createAndGetSetting(
            code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING,
            value: 'true',
            settingGroup: $this->getEntityManager()
                ->getRepository(SettingGroup::class)
                ->findOneBy(['code' => SettingGroupCode::ANNOUNCEMENTS])
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('providerValidationError')]
    public function testValidationErrors(
        int $announcementId,
        int $userId,
        int $expectedStatusCode,
        string $expectedMessage,
        bool $createAnnouncement = false
    ): void {
        $userToken = $this->loginAndGetToken();

        if ($createAnnouncement) {
            $course = $this->createAndGetCourse();
            $announcement = $this->createAndGetAnnouncement(course: $course);
            $announcementId = $announcement->getId();
        }

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcementId,
                userId: $userId
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertEquals($expectedMessage, $content['message']);
    }

    public static function providerValidationError(): \Generator
    {
        yield 'Invalid announcement ID' => [
            'announcementId' => -1,
            'userId' => -1,
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedMessage' => 'Validation failed',
        ];

        yield 'Invalid user ID' => [
            'announcementId' => 1,
            'userId' => -1,
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedMessage' => 'Validation failed',
        ];

        yield 'Announcement not found' => [
            'announcementId' => 9999,
            'userId' => 1,
            'expectedStatusCode' => Response::HTTP_NOT_FOUND,
            'expectedMessage' => 'Announcement not found',
        ];

        yield 'User not found' => [
            'announcementId' => 0,
            'userId' => 9999,
            'expectedStatusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'expectedMessage' => 'User not found',
            'createAnnouncement' => true,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('providerSuccessfulDelete')]
    public function testSuccessfulDelete(
        array $requestUserRoles,
        string $requestUserEmail,
        bool $isAnnouncementOwner
    ): void {
        $course = $this->createAndGetCourse();

        $requestUser = $this->createAndGetUser(
            firstName: 'Request',
            lastName: 'User',
            roles: $requestUserRoles,
            email: $requestUserEmail
        );

        $this->usersIds[] = $requestUser->getId();

        // Create announcement owner if needed
        $announcementOwner = $isAnnouncementOwner ? $requestUser : null;

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $announcementOwner
        );

        // Create manager to be deleted
        $manager = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $manager->getId();

        $this->setAndGetAnnouncementManager(
            userId: $manager->getId(),
            announcementId: $announcement->getId(),
        );

        // Login as the appropriate user
        $userToken = $this->loginAndGetTokenForUser($requestUser);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcement->getId(),
                userId: $manager->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    public static function providerSuccessfulDelete(): \Generator
    {
        yield 'Admin user' => [
            'requestUserRoles' => [User::ROLE_ADMIN],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
        ];

        yield 'Announcement owner' => [
            'requestUserRoles' => [User::ROLE_MANAGER],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => true,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('providerForbiddenRequest')]
    public function testForbiddenRequest(
        string $settingValue,
        array $requestUserRoles,
        string $requestUserEmail,
        bool $isAnnouncementOwner,
        string $expectedMessage
    ): void {
        $this->updateSettingValue(value: $settingValue, code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING);

        // Create announcement owner if needed
        $announcementOwner = null;
        if ($isAnnouncementOwner) {
            $announcementOwner = $this->createAndGetUser(
                firstName: 'Announcement',
                lastName: 'Owner',
                roles: [User::ROLE_MANAGER],
                email: '<EMAIL>'
            );
            $this->usersIds[] = $announcementOwner->getId();
        }

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course, createdBy: $announcementOwner);

        $requestUser = $this->createAndGetUser(
            firstName: 'Request',
            lastName: 'User',
            roles: $requestUserRoles,
            email: $requestUserEmail
        );

        $this->usersIds[] = $requestUser->getId();

        // Create manager to be deleted
        $manager = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $manager->getId();

        $this->setAndGetAnnouncementManager(
            userId: $manager->getId(),
            announcementId: $announcement->getId(),
        );

        // Login as the appropriate user
        $userToken = $this->loginAndGetTokenForUser($requestUser);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcement->getId(),
                userId: $manager->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString($expectedMessage, $content['message']);
    }

    public static function providerForbiddenRequest(): \Generator
    {
        yield 'Sharing disabled' => [
            'settingValue' => 'false',
            'requestUserRoles' => [User::ROLE_ADMIN],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
            'expectedMessage' => 'Announcement manager sharing is disabled',
        ];

        yield 'Manager not owner' => [
            'settingValue' => 'true',
            'requestUserRoles' => [User::ROLE_MANAGER],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => true,
            'expectedMessage' => 'is not authorized to perform this action',
        ];

        yield 'Creator role (not manager or admin)' => [
            'settingValue' => 'true',
            'requestUserRoles' => [User::ROLE_CREATOR],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
            'expectedMessage' => 'Forbidden',
        ];

        yield 'Multiple roles (Creator and Tutor)' => [
            'settingValue' => 'true',
            'requestUserRoles' => [User::ROLE_CREATOR, User::ROLE_TUTOR],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
            'expectedMessage' => 'Forbidden',
        ];

        yield 'Tutor role (not manager or admin)' => [
            'settingValue' => 'true',
            'requestUserRoles' => [User::ROLE_TUTOR],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
            'expectedMessage' => 'Forbidden',
        ];

        yield 'Regular user (not manager or admin)' => [
            'settingValue' => 'true',
            'requestUserRoles' => [User::ROLE_USER],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
            'expectedMessage' => 'Forbidden',
        ];

        yield 'Empty roles (guest access)' => [
            'settingValue' => 'true',
            'requestUserRoles' => [],
            'requestUserEmail' => '<EMAIL>',
            'isAnnouncementOwner' => false,
            'expectedMessage' => 'Forbidden',
        ];
    }

    public function testUnauthorizedWithoutToken(): void
    {
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: 1, userId: 1),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws MappingException
     * @throws NotSupported
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            AnnouncementManager::class,
            Announcement::class,
            Course::class,
        ]);

        if (null !== $this->originalSettingValue) {
            // The Setting existed originally, restore its value
            $this->updateSettingValue(value: $this->originalSettingValue, code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING);
        } else {
            // The Setting didn't exist originally, remove it
            $setting = $this->getEntityManager()
                ->getRepository(Setting::class)
                ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

            if ($setting) {
                $this->getEntityManager()->remove($setting);
                $this->getEntityManager()->flush();
            }
        }

        $this->hardDeleteUsersByIds($this->usersIds);
        parent::tearDown();
    }
}
