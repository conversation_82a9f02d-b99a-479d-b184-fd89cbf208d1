<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\HigherLower;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<HigherLower>
 *
 * @method HigherLower|null find($id, $lockMode = null, $lockVersion = null)
 * @method HigherLower|null findOneBy(array $criteria, array $orderBy = null)
 * @method HigherLower[]    findAll()
 * @method HigherLower[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class HigherLowerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, HigherLower::class);
    }

    public function add(HigherLower $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(HigherLower $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @return HigherLower[] Returns an array of HigherLower objects
     */
    public function findWordsOrdered($value): array
    {
        return $this->createQueryBuilder('h')
            ->select('w.id, w.word, w.position')
            ->join('h.higherLowerWords', 'w')
            ->andWhere('h.chapter = :val')
            ->setParameter('val', $value)
            ->orderBy('w.position', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }
}
