<?php

declare(strict_types=1);

namespace App\Controller\Apiv1\lti;

use App\Controller\Apiv1\ApiBaseController;
use App\Entity\LtiChapter;
use App\Entity\User;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use OAT\Library\Lti1p3Core\Message\Payload\LtiMessagePayloadInterface;
use OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface;
use OAT\Library\Lti1p3Core\Resource\LtiResourceLink\LtiResourceLink;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @deprecated Migration to V2 required
 */
class LtiController extends ApiBaseController
{
    /** @var PlatformOriginatingLaunchBuilder */
    private $builder;
    /** @var RegistrationRepositoryInterface */
    private $repository;
    /* Default login type */

    public function __construct(
        PlatformOriginatingLaunchBuilder $builder,
        RegistrationRepositoryInterface $repository,
        EntityManagerInterface $entityManager,
        SettingsService $settings
    ) {
        $this->builder = $builder;
        $this->repository = $repository;
        $this->entityManager = $entityManager;
        $this->settings = $settings;
    }

    /**
     * @Route("/.well-known/openid-configuration", name="lti_jwk_redirect")
     */
    public function jwkRedrect(Request $request)
    {
        $client = new \GuzzleHttp\Client();
        $res = $client->request('GET', 'https://' . $_SERVER['HTTP_HOST'] . '/lti1p3/.well-known/jwks/platformSet.json');
        $response = $res->getBody();

        $response = new Response(json_encode($response));
        $response->headers->set('Content-Type', 'application/json');
        $response->setContent($res->getBody());

        return $response;
    }

    /**
     * @Route("/lti/{userId}/{chapterId}", name="show_lti_course")
     */
    public function showLtiCourseLink(
        $userId,
        $chapterId,
        RegistrationRepositoryInterface $repository,
        LtiResourceLinkLaunchRequestBuilder $ltiResourceLinkLaunchRequestBuilder,
        Request $request,
    ): Response {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user) {
            return new Response('Must be logged in', Response::HTTP_UNAUTHORIZED);
        }

        $ltiChapter = $this->entityManager->getRepository(LtiChapter::class)->getIdenfierFromChapter($chapterId);
        if (null === $ltiChapter) {
            throw new \ErrorException('ltiChapter is empty. Missing reference.');
        }

        $customId = (null != $ltiChapter) ? $ltiChapter['identifier'] : '';

        $resourceLink = new LtiResourceLink(
            $customId,
            [
                'url' => $ltiChapter['launchUrl'],
            ]
        );

        $message = $ltiResourceLinkLaunchRequestBuilder->buildLtiResourceLinkLaunchRequest(
            $resourceLink,
            $repository->find($ltiChapter['name']),
            base64_encode(json_encode(['id' => $user->getId()])),
            $ltiChapter['deploymentsIds'],
            [
                'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner',
            ],
            [
                LtiMessagePayloadInterface::CLAIM_LTI_CUSTOM => [
                    'id' => $customId,
                ],
                LtiMessagePayloadInterface::CLAIM_LTI_AGS => [
                    'scope' => [
                        'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',   // Allow tool to send lineItems
                        'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly', // Allow tool to send results
                        'https://purl.imsglobal.org/spec/lti-ags/scope/score', // Allow tool to send scores
                    ],
                    /*
                     * By default lineitems is the url to get all lineItems of a resource link id
                     * Every other url GET|POST|PUT|DELETE for lineitem, results and score will have
                     * lineitems URL as base.
                     */
                    'lineitems' => $request->getSchemeAndHttpHost() . '/lti/' . $user->getId() . '/' . $customId . '/lineitems/',
                    'lineitem' => $request->getSchemeAndHttpHost() . '/lti/' . $user->getId() . '/' . $customId . '/lineitems',
                ],
            ],
            $ltiChapter['name'],
            $this->entityManager
        );

        return new Response($message->toHtmlRedirectForm());
    }
}
