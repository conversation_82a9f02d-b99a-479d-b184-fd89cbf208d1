# Override para desarrollo local en Windows
# Este archivo permite sobrescribir configuraciones específicas para Windows

services:
  backend_php:
    # En Windows, removemos la configuración de user que causa problemas
    user: ""
    build:
      context: ./docker/php
      args:
        USER_ID: "1000"
        GROUP_ID: "1000"
    # Configuración adicional para Windows
    environment:
      - SYMFONY_HOME=/var/www/html/.symfony
      - COMPOSER_CACHE_DIR=/var/www/html/.composer
    # Volúmenes adicionales para mejorar rendimiento en Windows
    volumes:
      - ./:/var/www/html/
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
      - ./docker/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
      - ./docker/php/error_reporting.ini:/usr/local/etc/php/conf.d/error_reporting.ini
      # Cache de Composer para mejorar rendimiento
      - composer_cache:/var/www/html/.composer

volumes:
  composer_cache:
    driver: local
