# Script PowerShell para comparar estructura de base de datos
# Uso: .\compare-database-structure.ps1

param(
    [string]$OutputDir = ".\scripts\db-comparison",
    [switch]$IncludeV2,
    [switch]$V2Only,
    [switch]$Help
)

if ($Help) {
    Write-Host "Uso: .\compare-database-structure.ps1 [-OutputDir <directorio>] [-IncludeV2] [-V2Only]"
    Write-Host ""
    Write-Host "Parámetros:"
    Write-Host "  -OutputDir    Directorio donde guardar los archivos (default: .\scripts\db-comparison)"
    Write-Host "  -IncludeV2    Incluir análisis de tablas V2 además de Doctrine"
    Write-Host "  -V2On<PERSON>       <PERSON><PERSON>zar solo tablas V2 (sin Doctrine)"
    Write-Host "  -Help         Mostrar esta ayuda"
    exit 0
}

# Función para logging con colores
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        default { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
    }
}

# Función para ejecutar comandos Docker
function Invoke-DockerCommand {
    param([string]$Command, [string]$Description)
    
    Write-Log "Ejecutando: $Description..."
    
    try {
        $result = Invoke-Expression $Command
        Write-Log "$Description completado" "SUCCESS"
        return $result
    }
    catch {
        Write-Log "Error en $Description`: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Crear directorio de salida
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

Write-Log "Iniciando comparación de estructura de base de datos..."

try {
    # 1. Verificar que Docker esté corriendo
    Write-Log "Verificando contenedores Docker..."
    $containers = docker ps --format "table {{.Names}}" | Select-String "easylearning-backend-php"

    if (!$containers) {
        Write-Log "Contenedor easylearning-backend-php no está corriendo" "ERROR"
        Write-Log "Ejecuta: docker-compose up -d" "WARNING"
        exit 1
    }

    # Determinar qué análisis ejecutar
    $analysisFlags = ""
    if ($IncludeV2) { $analysisFlags += " --include-v2" }
    if ($V2Only) { $analysisFlags += " --v2-only" }
    
    # 2. Ejecutar análisis completo usando el comando personalizado
    Write-Log "Ejecutando análisis completo de estructura..."
    $analysisFile = Join-Path $OutputDir "complete-analysis-$timestamp.txt"
    $analysisCommand = "docker exec easylearning-backend-php symfony console app:database:compare-structure$analysisFlags"
    $analysisResult = Invoke-DockerCommand $analysisCommand "Análisis completo de estructura"
    $analysisResult | Out-File -FilePath $analysisFile -Encoding UTF8
    Write-Log "Análisis completo guardado en: $analysisFile" "SUCCESS"

    # 3. Generar diferencias Doctrine (si no es solo V2)
    if (!$V2Only) {
        Write-Log "Generando diferencias Doctrine..."
        $diffFile = Join-Path $OutputDir "doctrine-diff-$timestamp.sql"
        $diffCommand = "docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete"
        $diffSql = Invoke-DockerCommand $diffCommand "Generación de diferencias Doctrine"
        $diffSql | Out-File -FilePath $diffFile -Encoding UTF8
        Write-Log "Diferencias Doctrine guardadas en: $diffFile" "SUCCESS"
    }
    
    # 4. Validar esquema
    Write-Log "Validando esquema..."
    $validationFile = Join-Path $OutputDir "schema-validation-$timestamp.txt"
    $validationCommand = "docker exec easylearning-backend-php symfony console doctrine:schema:validate"
    try {
        $validation = Invoke-DockerCommand $validationCommand "Validación de esquema"
        $validation | Out-File -FilePath $validationFile -Encoding UTF8
        $isValid = $validation -match "\[OK\]"
        Write-Log "Validación guardada en: $validationFile" "SUCCESS"
    }
    catch {
        $isValid = $false
        "Error en validación: $($_.Exception.Message)" | Out-File -FilePath $validationFile -Encoding UTF8
    }
    
    # 5. Listar migraciones
    Write-Log "Listando migraciones..."
    $migrationsFile = Join-Path $OutputDir "migrations-status-$timestamp.txt"
    $migrationsCommand = "docker exec easylearning-backend-php symfony console doctrine:migrations:list"
    $migrations = Invoke-DockerCommand $migrationsCommand "Listado de migraciones"
    $migrations | Out-File -FilePath $migrationsFile -Encoding UTF8
    Write-Log "Estado de migraciones guardado en: $migrationsFile" "SUCCESS"
    
    # 6. Exportar estructura de BD actual
    Write-Log "Exportando estructura de base de datos actual..."
    $structureFile = Join-Path $OutputDir "db-structure-$timestamp.sql"
    $structureCommand = "docker exec -i easylearning-database mysqldump -h localhost -u root -pdocker --no-data --routines --triggers easylearning"
    try {
        $structure = Invoke-DockerCommand $structureCommand "Exportación de estructura"
        $structure | Out-File -FilePath $structureFile -Encoding UTF8
        Write-Log "Estructura exportada en: $structureFile" "SUCCESS"
    }
    catch {
        Write-Log "No se pudo exportar la estructura de BD actual" "WARNING"
    }
    
    # 7. Generar reporte
    Write-Log "Generando reporte..."
    $reportFile = Join-Path $OutputDir "database-comparison-report-$timestamp.md"
    
    $report = @"
# Reporte de Comparación de Base de Datos

**Fecha:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")  
**Timestamp:** $timestamp

## Archivos Generados

- [Esquema Actual](./$(Split-Path $schemaFile -Leaf))
- [Diferencias](./$(Split-Path $diffFile -Leaf))
- [Validación](./$(Split-Path $validationFile -Leaf))
- [Estado Migraciones](./$(Split-Path $migrationsFile -Leaf))
$(if (Test-Path $structureFile) { "- [Estructura BD](./$(Split-Path $structureFile -Leaf))" })

## Estado del Esquema

$(if ($isValid) { "✅ **ESQUEMA VÁLIDO**" } else { "❌ **ESQUEMA INVÁLIDO**" })

## Comandos Útiles

### Ver diferencias:
``````powershell
docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete
``````

### Aplicar diferencias (¡CUIDADO!):
``````powershell
docker exec easylearning-backend-php symfony console doctrine:schema:update --force --complete
``````

### Generar migración:
``````powershell
docker exec easylearning-backend-php symfony console doctrine:migrations:diff
``````

### Aplicar migraciones:
``````powershell
docker exec easylearning-backend-php symfony console doctrine:migrations:migrate
``````

## ⚠️ Advertencias

- **SIEMPRE** hacer backup antes de aplicar cambios
- Probar en entorno de desarrollo primero
- Revisar manualmente todas las diferencias

"@
    
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Log "Reporte generado: $reportFile" "SUCCESS"
    
    # Mostrar resumen
    Write-Host ""
    Write-Host "📊 RESUMEN DE COMPARACIÓN" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    Write-Host "Estado del esquema: $(if ($isValid) { '✅ VÁLIDO' } else { '❌ INVÁLIDO' })"
    Write-Host ""
    Write-Host "📁 Archivos generados en: $OutputDir" -ForegroundColor Green
    Write-Host "📋 Ver reporte completo: $reportFile" -ForegroundColor Green
    
    # Verificar si hay diferencias
    if ((Get-Content $diffFile | Measure-Object -Line).Lines -gt 1) {
        Write-Log "Se encontraron diferencias en el esquema" "WARNING"
        Write-Host "   Revisa el archivo: $diffFile" -ForegroundColor Yellow
    } else {
        Write-Log "No se encontraron diferencias en el esquema" "SUCCESS"
    }
    
    Write-Log "Comparación completada exitosamente!" "SUCCESS"
    
} catch {
    Write-Log "Error durante la comparación: $($_.Exception.Message)" "ERROR"
    exit 1
}
"@
