#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Configuración
const OUTPUT_DIR = './scripts/db-comparison';
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);

// Tablas V2 definidas
const V2_TABLES = [
    'course_creator',
    'announcement_manager',
    'purchasable_item',
    'subscription',
    'lti_registration',
    'lti_platform',
    'lti_tool_v2',
    'lti_deployment',
    'user_filter',
    'manager_filter'
];

// Crear directorio de salida si no existe
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Ejecutar comando y capturar salida
 */
function runCommand(command, description) {
    console.log(`🔄 ${description}...`);
    try {
        const result = execSync(command, { encoding: 'utf8', cwd: process.cwd() });
        console.log(`✅ ${description} completado`);
        return result;
    } catch (error) {
        console.error(`❌ Error en ${description}:`, error.message);
        throw error;
    }
}

/**
 * Parsear DATABASE_URL
 */
function parseDatabaseUrl(url) {
    const match = url.match(/mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!match) {
        throw new Error('Formato de DATABASE_URL inválido');
    }
    
    return {
        host: match[3],
        port: parseInt(match[4]),
        user: match[1],
        password: match[2],
        database: match[5]
    };
}

/**
 * Conectar a la base de datos
 */
async function connectToDatabase() {
    const databaseUrl = process.env.DATABASE_URL || 'mysql://root:docker@127.0.0.1:3316/easylearning';
    const config = parseDatabaseUrl(databaseUrl);
    
    console.log(`🔌 Conectando a ${config.host}:${config.port}/${config.database}...`);
    
    const connection = await mysql.createConnection(config);
    console.log('✅ Conexión establecida');
    
    return connection;
}

/**
 * Analizar tablas V2
 */
async function analyzeV2Tables(connection) {
    console.log('\n📋 Analizando tablas V2...');
    
    const analysis = {
        existing_tables: [],
        missing_tables: [],
        table_structures: {},
        migration_suggestions: []
    };

    for (const tableName of V2_TABLES) {
        try {
            // Verificar si la tabla existe
            const [tables] = await connection.execute(
                'SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?',
                [tableName]
            );

            if (tables.length > 0) {
                analysis.existing_tables.push(tableName);
                
                // Obtener estructura de la tabla
                const [columns] = await connection.execute(
                    `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA 
                     FROM information_schema.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? 
                     ORDER BY ORDINAL_POSITION`,
                    [tableName]
                );

                const [indexes] = await connection.execute(
                    `SELECT INDEX_NAME, COLUMN_NAME, NON_UNIQUE 
                     FROM information_schema.STATISTICS 
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?`,
                    [tableName]
                );

                const [foreignKeys] = await connection.execute(
                    `SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
                     FROM information_schema.KEY_COLUMN_USAGE 
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND REFERENCED_TABLE_NAME IS NOT NULL`,
                    [tableName]
                );

                analysis.table_structures[tableName] = {
                    columns: columns.map(col => ({
                        name: col.COLUMN_NAME,
                        type: col.DATA_TYPE,
                        nullable: col.IS_NULLABLE === 'YES',
                        default: col.COLUMN_DEFAULT,
                        extra: col.EXTRA
                    })),
                    indexes: indexes.map(idx => ({
                        name: idx.INDEX_NAME,
                        column: idx.COLUMN_NAME,
                        unique: idx.NON_UNIQUE === 0
                    })),
                    foreign_keys: foreignKeys.map(fk => ({
                        name: fk.CONSTRAINT_NAME,
                        column: fk.COLUMN_NAME,
                        referenced_table: fk.REFERENCED_TABLE_NAME,
                        referenced_column: fk.REFERENCED_COLUMN_NAME
                    }))
                };

                console.log(`  ✅ ${tableName} - ${columns.length} columnas`);
            } else {
                analysis.missing_tables.push(tableName);
                console.log(`  ❌ ${tableName} - NO EXISTE`);
                
                // Sugerir migración basada en migraciones existentes
                analysis.migration_suggestions.push(await suggestMigrationForTable(tableName));
            }
        } catch (error) {
            console.error(`  ❌ Error analizando ${tableName}:`, error.message);
            analysis.missing_tables.push(tableName);
        }
    }

    return analysis;
}

/**
 * Sugerir migración para tabla faltante
 */
async function suggestMigrationForTable(tableName) {
    // Buscar en migraciones existentes
    const migrationsDir = './src/Migrations';
    const migrationFiles = fs.readdirSync(migrationsDir).filter(file => file.endsWith('.php'));
    
    for (const file of migrationFiles) {
        const content = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
        if (content.includes(`CREATE TABLE ${tableName}`) || content.includes(`CREATE TABLE \`${tableName}\``)) {
            return {
                table: tableName,
                found_in_migration: file,
                suggestion: `Ejecutar migración específica o crear tabla manualmente basada en ${file}`
            };
        }
    }
    
    return {
        table: tableName,
        found_in_migration: null,
        suggestion: `Crear migración manual para tabla ${tableName} - verificar definición en repositorios DBAL`
    };
}

/**
 * Generar reporte detallado
 */
function generateDetailedReport(analysis) {
    const reportFile = path.join(OUTPUT_DIR, `v2-analysis-${TIMESTAMP}.md`);
    
    const report = `# Análisis de Tablas V2

**Fecha:** ${new Date().toLocaleString()}  
**Tablas analizadas:** ${V2_TABLES.length}

## Resumen

- ✅ **Tablas existentes:** ${analysis.existing_tables.length}
- ❌ **Tablas faltantes:** ${analysis.missing_tables.length}
- 📊 **Porcentaje completitud:** ${Math.round((analysis.existing_tables.length / V2_TABLES.length) * 100)}%

## Tablas Existentes

${analysis.existing_tables.map(table => {
    const structure = analysis.table_structures[table];
    return `### ✅ ${table}

**Columnas:** ${structure.columns.length}
${structure.columns.map(col => `- \`${col.name}\` (${col.type}${col.nullable ? ', nullable' : ''})`).join('\n')}

**Índices:** ${structure.indexes.length}
${structure.indexes.map(idx => `- \`${idx.name}\` en \`${idx.column}\`${idx.unique ? ' (único)' : ''}`).join('\n')}

**Claves foráneas:** ${structure.foreign_keys.length}
${structure.foreign_keys.map(fk => `- \`${fk.column}\` → \`${fk.referenced_table}.\`${fk.referenced_column}\``).join('\n')}
`;
}).join('\n')}

## Tablas Faltantes

${analysis.missing_tables.map(table => {
    const suggestion = analysis.migration_suggestions.find(s => s.table === table);
    return `### ❌ ${table}

${suggestion ? `**Migración encontrada:** ${suggestion.found_in_migration || 'No encontrada'}
**Sugerencia:** ${suggestion.suggestion}` : 'No se encontró información de migración'}
`;
}).join('\n')}

## Comandos Útiles

### Para crear tablas faltantes:
\`\`\`bash
# Ejecutar migraciones pendientes
make console-win -- doctrine:migrations:migrate

# Verificar estado de migraciones
make migrations-status
\`\`\`

### Para análisis completo:
\`\`\`bash
# Análisis completo (Doctrine + V2)
make analyze-structure

# Solo tablas V2
make analyze-structure-v2-only
\`\`\`

## Definiciones de Tablas V2

Las tablas V2 están definidas en:
- \`config/services/v2.yaml\` - Nombres de tablas
- \`src/V2/Infrastructure/Persistence/\` - Repositorios DBAL
- \`src/Migrations/\` - Migraciones específicas

## Próximos Pasos

${analysis.missing_tables.length > 0 ? `
1. **Revisar migraciones:** Verificar si hay migraciones pendientes
2. **Crear tablas faltantes:** Ejecutar migraciones o crear manualmente
3. **Validar estructura:** Comparar con definiciones en repositorios DBAL
` : `
✅ **Todas las tablas V2 están presentes**
- Verificar que las estructuras coincidan con las definiciones
- Considerar ejecutar análisis completo con Doctrine
`}
`;

    fs.writeFileSync(reportFile, report);
    console.log(`📊 Reporte detallado generado: ${reportFile}`);
    
    return reportFile;
}

/**
 * Función principal
 */
async function main() {
    console.log('🚀 Iniciando análisis de tablas V2...\n');
    
    let connection;
    
    try {
        // Conectar a la base de datos
        connection = await connectToDatabase();
        
        // Analizar tablas V2
        const analysis = await analyzeV2Tables(connection);
        
        // Generar reporte detallado
        const reportFile = generateDetailedReport(analysis);
        
        // Mostrar resumen
        console.log('\n📊 RESUMEN DEL ANÁLISIS');
        console.log('======================');
        console.log(`Tablas V2 definidas: ${V2_TABLES.length}`);
        console.log(`Tablas existentes: ${analysis.existing_tables.length}`);
        console.log(`Tablas faltantes: ${analysis.missing_tables.length}`);
        console.log(`Completitud: ${Math.round((analysis.existing_tables.length / V2_TABLES.length) * 100)}%`);
        
        if (analysis.missing_tables.length > 0) {
            console.log('\n❌ Tablas faltantes:');
            analysis.missing_tables.forEach(table => console.log(`  - ${table}`));
            console.log('\n💡 Ejecuta migraciones pendientes o revisa el reporte para más detalles');
        } else {
            console.log('\n✅ Todas las tablas V2 están presentes');
        }
        
        console.log(`\n📋 Ver reporte completo: ${reportFile}`);
        console.log('\n🎉 Análisis completado exitosamente!');
        
    } catch (error) {
        console.error('\n❌ Error durante el análisis:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
    main();
}

module.exports = { main, analyzeV2Tables, V2_TABLES };
