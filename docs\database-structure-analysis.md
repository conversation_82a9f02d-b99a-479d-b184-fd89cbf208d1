# Análisis de Estructura de Base de Datos

Este documento explica cómo analizar las diferencias entre la estructura definida en el código (Doctrine + V2) y la base de datos real.

## Sistemas de Gestión de Esquemas

El proyecto utiliza dos sistemas diferentes para manejar la estructura de base de datos:

### 1. **Sistema Doctrine ORM**
- **Ubicación:** `src/Entity/`
- **Migraciones:** `src/Migrations/`
- **Gestión:** Automática a través de entidades y migraciones
- **Comandos:** `doctrine:schema:*`, `doctrine:migrations:*`

### 2. **Sistema V2 (DBAL)**
- **Ubicación:** `src/V2/Infrastructure/Persistence/`
- **Configuración:** `config/services/v2.yaml`
- **Gestión:** Manual a través de repositorios DBAL
- **Tablas:**
  - `course_creator`
  - `announcement_manager`
  - `purchasable_item`
  - `subscription`
  - `lti_registration`
  - `lti_platform`
  - `lti_tool_v2`
  - `lti_deployment`
  - `user_filter`
  - `manager_filter`

## Herramientas de Análisis

### 1. **Comando Symfony Personalizado**

```bash
# Análisis completo (Doctrine + V2) con BD local
php bin/console app:database:compare-structure --include-v2

# Solo tablas V2
php bin/console app:database:compare-structure --v2-only

# Comparar con BD remota
php bin/console app:database:compare-structure mysql://user:pass@host:port/db --include-v2

# Guardar reporte
php bin/console app:database:compare-structure --include-v2 -o report.txt

# Solo diferencias
php bin/console app:database:compare-structure --include-v2 --only-differences
```

### 2. **Comandos Make**

```bash
# Análisis completo local
make analyze-structure

# Solo tablas V2
make analyze-structure-v2-only

# Con BD de cliente (requiere CLIENT_DSN)
make analyze-structure-client CLIENT_DSN=mysql://user:pass@host:port/db

# Generar reporte con timestamp
make analyze-structure-report
```

### 3. **Scripts Especializados**

#### Script JavaScript para V2:
```bash
node scripts/analyze-v2-tables.js
```

#### Script PowerShell (Windows):
```powershell
# Análisis completo
.\scripts\compare-database-structure.ps1 -IncludeV2

# Solo V2
.\scripts\compare-database-structure.ps1 -V2Only
```

## Tipos de Análisis

### **Análisis Doctrine**
- Compara entidades con estructura de BD
- Detecta tablas nuevas, eliminadas o modificadas
- Muestra diferencias en columnas, índices y claves foráneas
- Genera SQL para sincronización

### **Análisis V2**
- Verifica existencia de tablas V2
- Analiza estructura detallada de cada tabla
- Sugiere migraciones faltantes
- Identifica inconsistencias con definiciones

### **Análisis Completo**
- Combina ambos análisis
- Proporciona vista integral del estado
- Genera reportes unificados
- Incluye recomendaciones específicas

## Flujo de Trabajo Recomendado

### 1. **Análisis Inicial**
```bash
# Verificar estado general
make analyze-structure

# Si hay problemas, analizar por separado
make analyze-structure-v2-only
make databasediff-win
```

### 2. **Identificar Problemas**

#### Tablas V2 Faltantes:
```bash
# Buscar migraciones relacionadas
grep -r "CREATE TABLE table_name" src/Migrations/

# Ejecutar migraciones pendientes
make migrations-status
make console-win -- doctrine:migrations:migrate
```

#### Diferencias Doctrine:
```bash
# Ver diferencias específicas
make databasediff-win

# Generar migración
make migrations-diff

# Aplicar cambios (¡CUIDADO!)
make databasediff-force
```

### 3. **Comparar con Cliente**

```bash
# Exportar estructura del cliente
mysqldump -h CLIENT_HOST -u USER -pPASS --no-data --routines CLIENT_DB > client-structure.sql

# Comparar con estructura local
php bin/console app:database:compare-structure mysql://user:pass@client:3306/db --include-v2 -o client-comparison.txt
```

### 4. **Sincronización**

#### Para Doctrine:
```bash
# Generar SQL de sincronización
make databasediff-win > sync-doctrine.sql

# Revisar y aplicar manualmente
# O usar: make databasediff-force
```

#### Para V2:
```bash
# Las tablas V2 requieren migraciones manuales
# Buscar en src/Migrations/ las definiciones
# O crear migraciones personalizadas
```

## Interpretación de Resultados

### **Estados Posibles**

- ✅ **Estructura idéntica:** No hay diferencias
- ⚠️ **Diferencias menores:** Cambios en columnas o índices
- ❌ **Tablas faltantes:** Requiere migraciones
- 🔧 **Modificaciones:** Cambios estructurales importantes

### **Acciones Recomendadas**

| Estado | Doctrine | V2 |
|--------|----------|-----|
| Tablas faltantes | `doctrine:migrations:migrate` | Buscar migración específica |
| Columnas nuevas | `doctrine:schema:update` | Migración manual |
| Índices diferentes | `doctrine:schema:update` | Verificar repositorio DBAL |
| Claves foráneas | Revisar relaciones | Verificar constraints |

## Archivos de Salida

Los análisis generan archivos en `scripts/db-comparison/`:

- `complete-analysis-TIMESTAMP.txt` - Análisis completo
- `doctrine-diff-TIMESTAMP.sql` - Diferencias Doctrine
- `v2-analysis-TIMESTAMP.md` - Análisis detallado V2
- `structure-report-TIMESTAMP.txt` - Reporte unificado

## Solución de Problemas

### **Error: Tablas V2 no encontradas**
1. Verificar migraciones: `make migrations-status`
2. Buscar definiciones: `grep -r "table_name" src/Migrations/`
3. Ejecutar migraciones: `make console-win -- doctrine:migrations:migrate`

### **Error: Diferencias Doctrine**
1. Verificar entidades: `make schema-validate`
2. Generar migración: `make migrations-diff`
3. Revisar cambios antes de aplicar

### **Error: Conexión BD**
1. Verificar contenedores: `docker ps`
2. Verificar configuración: `.env.local`
3. Probar conexión: `make console-win -- doctrine:query:sql "SELECT 1"`

## Mejores Prácticas

1. **Siempre hacer backup** antes de aplicar cambios
2. **Probar en desarrollo** antes de producción
3. **Revisar manualmente** todas las diferencias
4. **Documentar cambios** importantes
5. **Usar análisis completo** para vista integral
6. **Verificar ambos sistemas** (Doctrine + V2)

## Comandos de Referencia Rápida

```bash
# Análisis rápido
make analyze-structure

# Diferencias Doctrine
make databasediff-win

# Estado migraciones
make migrations-status

# Validar esquema
make schema-validate

# Análisis V2 detallado
node scripts/analyze-v2-tables.js
```
