# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    ### Set value for specific clients
    ### Affect Announcements fields, it can be used in other entities where fields between clients are different
    ### Where default does not include content from specific clients, when specifying client, it returns fields/content
    ### related to the specific client and the default content
    app.announcement_client_identification: # default | imq | iberostar
        - 'default'
        - 'imq'
    app.default_timezone: 'Europe/Madrid'
    app.course_uploads_path: 'uploads/images/course'
    app.chapter_uploads_path: 'uploads/images/chapter'
    app.challenge_uploads_path: 'uploads/images/challenge'
    app.challenge_question_uploads_path: 'uploads/images/challenge/question'
    app.question_uploads_path: 'uploads/images/question'
    app.news_uploads_path: 'uploads/images/news'
    app.scorm_uploads_path: 'uploads/scorms/packages'
    pdf_uploads_path: 'uploads/pdf/packages'
    ppt_uploads_path: 'uploads/ppt/packages'
    video_uploads_path: 'uploads/video/packages'
    app.avatar_uploads_path: 'uploads/users/avatars'
    message_attachment_uploads_path: 'message_attachment'
    app.puzzle_uploads_path: 'uploads/puzzle/images'
    app.slider_uploads_path: 'uploads/slider/images'
    app.scorm_folders_path: 'uploads/scorms'
    app.user_resume_uploads_path: 'uploads/users/resumes'
    app.documentation_uploads: 'uploads/pdf/documentation'
    app.sharefile_uploads_path: 'uploads/sharefile/files'
    app.material_course_path: 'uploads/material_course'
    app.task_course_path: 'uploads/task_course'
    app.task_user_path: 'uploads/task_user'
    app.library_upload_path: 'uploads/library'
    app.gameTrueOrFalse_uploads_path: 'uploads/games/trueOrFalse'
    app.gameAdivinaImagen_uploads_path: 'uploads/games/adivinaImagen'
    app.gameOrdenarMenorMayor_uploads_path: 'uploads/games/ordenarMenorMayor'
    app.gameParejasImagen_uploads_path: 'uploads/games/parejas'
    app.gameCategorized_uploads_path: 'uploads/games/trueOrFalse'
    app.gameVideoquiz_uploads_path: 'uploads/games/videoquiz'
    app.gamecategorize_options_uploads_path: 'uploads/games/categorize_options'
    app.gameCategorize_uploads_path: 'uploads/games/categorize'
    app.pdf_cv_file_uploads_path: 'uploads/users/cv'
    app.announcement_observation_files_path: 'uploads/files/announcement_observation/'
    app.announcement_configuration_files_path: 'assets_announcement/configuration/'
    app.announcement_didactic_guide: 'assets_announcement/didactic_guide/'
    app.roleplay.image_uploads_path: 'uploads/roleplay/images'
    app.announcement_assistance_group_session: 'assets_announcement/assistance_group_session/'
    app.announcement_user_digital_signature: 'digital_signature/'
    app.file_manager.base_dir: '%kernel.project_dir%/files'
    app.authentication_logs: '%kernel.project_dir%/var/log/authentication'
    app.app_name: '%env(APP_NAME)%'

imports:
    - { resource: services/easylearning.yaml }
    - { resource: services/announcement.yaml }
    - { resource: services/ranking.yaml }
    - { resource: services/support.yaml }
    - { resource: services/sso.yaml }
    - { resource: services/export.yaml }
    - { resource: services/ldap.yaml }
    - { resource: services/saml.yaml }
    - { resource: services/stats.yaml }
    - { resource: services/oauth2.yaml }
    - { resource: services/challengeconfig.yaml }
    - { resource: services/library.yaml }
    - { resource: services/permissions.yaml }
    - { resource: services/classsvirtual.yaml }
    - { resource: services/user.yaml }
    - { resource: services/api.yaml }
    - { resource: services/user_verifier.yaml }
    - { resource: services/password.yaml }
    - { resource: services/integrations.yaml }
    - { resource: services/roleplay.yaml }
    - { resource: services/slot_manager.yaml }
    - { resource: services/v2.yaml }
    - { resource: services/date_formatter.yaml }
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            # Log
            $logPath: "%kernel.logs_dir%"

            # JWT
            int $jwtTokenTtl: '%env(JWT_TOKENTTL)%'

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude:
            - '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'
            - '../src/V2/'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    App\EntityListener\HashPasswordListener:
        tags:
            - { name: 'doctrine.orm.entity_listener', event: 'prePersist', entity: 'App\Entity\User' }
            - { name: 'doctrine.orm.entity_listener', event: 'preUpdate', entity: 'App\Entity\User' }
    App\EntityListener\ModifyingUserSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber'}
    App\EntityListener\DeletedByListener:
        tags:
            - { name: doctrine.event_listener, event: preSoftDelete, connection: default }
    App\EventSubscriber\TimezoneConverterSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber' }
    App\EventSubscriber\TimezoneSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber' }
    App\EventSubscriber\ChapterSubscriber:
        tags: [ 'kernel.event_subscriber' ]

    App\EntityListener\AbsoluteUrlHelper:
        arguments: ["%app.question_uploads_path%"]
        tags:
            - { name: 'doctrine.event_subscriber'}
    App\EntityListener\SetParameterSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber' }
    App\EntityListener\ImageableSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber' }
    App\EntityListener\CheckUsersCourseParamsListener:
        tags:
            - { name: 'doctrine.event_subscriber' }

    App\EntityListener\UserListener:
        tags:
            - { name: 'doctrine.orm.entity_listener', event: 'preFlush', entity: 'App\Entity\User' }


    App\EntityListener\ChapterListener:
        tags:
            - { name: 'doctrine.orm.entity_listener', event: 'prePersist', entity: 'App\Entity\Chapter' }

    App\EventListener\ChapterListener:
        tags:
            - { name: 'doctrine.event_listener', event: 'postSoftDelete', entity: 'App\Entity\Chapter' }

    App\Service\User\Authentication\StarTeam:
        bind:
            $tokenUrl: "%env(TOKEN_URL)%"
            $tokenKey: "%env(TOKEN_KEY)%"
            $userUrl: "%env(USER_URL)%"
            $userKey: "%env(USER_KEY)%"
            $apiUsername: "%env(API_USERNAME)%"
            $apiPass: "%env(API_PASS)%"
    Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager:
        autowire: true
    Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationSuccessHandler:
        autowire: true
    App\EntityListener\NotificationSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber', entity: 'App\Entity\Notification' }
    App\Form\Type\Admin\UserExtraType:
        arguments:
            - "%app.user.extrafields%"
    App\Form\Type\Admin\UserFilterType:
        arguments:
            - "%app.user.filterfields%"

    App\Controller\Admin\FroalaUploadController:
        arguments:
            $mediaManager: '@kms_froala_editor.media_manager'
    App\Saml\Saml2Service:
        autowire: true
    App\Games\GameFactory:
        autowire: true

    # Listener for all request received
    App\EventSubscriber\RequestSubscriber:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 1 }
        arguments:
            - "@logger"
    App\EventSubscriber\ResponseSubscriber:
        arguments:
            - "@logger"

    ## Refresh token services
    gesdinet.jwtrefreshtoken.send_token:
        class: App\Security\Authentication\AttachRefreshToken
        arguments:
            - '@gesdinet.jwtrefreshtoken.refresh_token_manager'
            - '%gesdinet_jwt_refresh_token.ttl%'
            - '@request_stack'
            - '%gesdinet_jwt_refresh_token.token_parameter_name%'
            - '%gesdinet_jwt_refresh_token.single_use%'
            - '@gesdinet.jwtrefreshtoken.refresh_token_generator'
            - '@gesdinet.jwtrefreshtoken.request.extractor.chain'
            - '%gesdinet_jwt_refresh_token.cookie%'
            - '%gesdinet_jwt_refresh_token.return_expiration%'
            - '%gesdinet_jwt_refresh_token.return_expiration_parameter_name%'
        tags:
            - { name: 'kernel.event_listener', event: 'lexik_jwt_authentication.on_authentication_success', method: 'attachRefreshToken'}

    lexik_jwt_authentication.handler.authentication_success:
        class: App\Security\Authentication\LexitAutenticationSuccessHandler
        arguments:
            - '@lexik_jwt_authentication.jwt_manager'
            - '@event_dispatcher'

    App\EventListener\RateLimiterExceptionListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    ## External Announcement Repository
    App\Repository\ExternalAnnouncement\IberostarExternalAnnouncementRepository:
    App\Repository\ExternalAnnouncement\ExternalAnnouncementRepositories:
        arguments:
            -
                - '@App\Repository\ExternalAnnouncement\IberostarExternalAnnouncementRepository'

    # ----> LTI
    App\Lti\ScoreService:
            arguments:
                $em: '@doctrine.orm.entity_manager'  # Esto inyecta el EntityManager
                $scoreRepository: '@App\Lti\ScoreRepository'  # Inyecta ScoreRepository

    App\Controller\Apiv1\lti\LtiLineItemController:
        arguments:
            $scoreService: '@App\Lti\ScoreService'

    App\Lti\ScoreRepository:
        arguments:
            $ltiLineItemScoreRepository: '@App\Repository\LtiLineItemScoreRepository'
            $em: '@doctrine.orm.entity_manager'

    OAT\Library\Lti1p3Core\Security\User\UserAuthenticatorInterface:
        class: App\Controller\Apiv1\lti\AuthenticateLtiController

    OAT\Library\Lti1p3Core\Security\Oidc\OidcAuthenticator:
        class: App\Controller\Apiv1\lti\OidcAuthenticatorLtiController

    OAT\Library\Lti1p3Core\Message\Launch\Builder\LtiResourceLinkLaunchRequestBuilder:
        class: App\Controller\Apiv1\lti\LtiResourceLinkLaunchRequestBuilder

    OAT\Library\Lti1p3Core\Message\Launch\Builder\PlatformOriginatingLaunchBuilder:
        class: App\Controller\Apiv1\lti\PlatformOriginatingLaunchBuilder

    OAT\Bundle\Lti1p3Bundle\DependencyInjection\Builder\RegistrationRepositoryBuilder:
        class: App\Controller\Apiv1\lti\RegistrationRepositoryBuilder

    OAT\Library\Lti1p3Core\Security\Jwt\Builder\Builder:
        class: App\Controller\Apiv1\lti\BuilderLtiController

    #LTI <-----

    ## Integrations clients
    App\Security\Integrations\Clients\ChefBurgerClientConfigurator:
    App\Security\Integrations\Clients\VicioSchoolClientConfigurator:
    App\Security\Integrations\Clients\ChefBurgerClient:
        configurator: '@App\Security\Integrations\Clients\ChefBurgerClientConfigurator'
    App\Security\Integrations\Clients\VicioSchoolClient:
        configurator: '@App\Security\Integrations\Clients\VicioSchoolClientConfigurator'

    App\Security\Integrations\Clients\IntegrationClients:
        arguments:
            $clients:
                - '@App\Security\Integrations\Clients\VicioSchoolClient'
                - '@App\Security\Integrations\Clients\ChefBurgerClient'
                - '@App\Security\Integrations\Clients\LdapClient'
                - 'App\Controller\Apiv1\lti\MembershipServiceServerBuilder'

    App\Service\Diploma\DiplomaService:
        public: true

    App\EventListener\CreatorAccessListener:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 4 }
        arguments:
            - '@security.helper'
            - '@router'

    # Mass Import Logger Autowiring
    App\Service\Annoucement\Admin\ExternalAnnouncementParticipantImportService:
        arguments:
            $massImportLogger: '@monolog.logger.mass_import'

    App\Service\Annoucement\Admin\ExternalAnnouncementImportService:
        arguments:
            $massImportLogger: '@monolog.logger.mass_import'


