<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Shared\Resource;

use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Resource\ResourceType;

class ResourceMother
{
    private const ResourceType DEFAULT_TYPE = ResourceType::Course;
    private const int DEFAULT_ID = 1;

    public static function create(
        ?ResourceType $type = null,
        ?Identifier $id = null,
    ): Resource {
        return new Resource(
            type: $type ?? self::DEFAULT_TYPE,
            id: $id ?? new Id(self::DEFAULT_ID),
        );
    }
}
