<template>
  <div class="HeaderUserPopup">
    <div class="headerContent">
      <label>Conectado/a como</label>
      <p class="userName">{{ fullName }}</p>
    </div>
    <div class="headerContent">
      <span
        v-if="!hideEdit"
        class="link"
        @click.stop="openRoute({ name: '' })"
        ><Icon
          class="icon"
          icon="user-cog"
        />
        {{ $t('USER.UPDATE') }}
      </span>
      <span
        class="link"
        @click.stop="logout()"
        ><Icon
          class="icon"
          icon="sign-out"
        />
        {{ $t('INSPECTORVIEW.LOGOUT') }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { inject } from 'vue'
const { openRoute, logout } = inject('LayoutEmits')

defineProps({
  fullName: {
    type: String,
    default: '',
  },
  hideEdit: { type: Boolean, default: false },
})
</script>

<style scoped lang="scss">
.HeaderUserPopup {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: absolute;
  top: calc(100% + 1.5rem);
  width: 210px;
  background-color: var(--color-neutral-lightest);
  padding: 0.5rem 1rem;
  border-radius: 3px;
  border: 1px solid var(--color-neutral-mid);
  cursor: initial;

  .headerContent {
    display: flex;
    flex-direction: column;
    color: var(--color-neutral-mid-dark);
  }

  label {
    font-size: 0.85rem;
  }

  .userName {
    color: var(--color-neutral-darker);
    margin: 0;
  }

  .link {
    color: var(--color-primary-dark);
    cursor: pointer;

    .icon {
      width: 1.3rem;
    }

    &:hover {
      color: var(--color-primary);
      text-decoration: underline;
    }
  }

  &:after {
    content: ' ';
    position: absolute;
    width: 1rem;
    height: 1rem;
    transform: rotate(45deg);
    top: -0.5rem;
    left: calc(50% - 0.5rem);
    background-color: var(--color-neutral-lightest);
    border: solid var(--color-neutral-mid);
    border-width: 1px 0 0 1px;
  }
}
</style>
