# Configuración simplificada para Windows - solo base de datos
services:
  database:
    container_name: easylearning-database
    image: mariadb:10.5
    restart: unless-stopped
    ports:
      - "3316:3306"
    volumes:
      - ./docker/db:/var/lib/mysql
      - ./docker/mariadb/initdb:/docker-entrypoint-initdb.d
    environment:
      MYSQL_ROOT_PASSWORD: docker
      MYSQL_DATABASE: easylearning
    command: --max_allowed_packet=64505856

  mailer:
    container_name: easylearning-mailer
    image: axllent/mailpit
    ports:
      - "11025:1025"
      - "18025:8025"
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
