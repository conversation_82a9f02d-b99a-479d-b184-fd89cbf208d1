import { USER_ROLE_NAMES } from '@/contexts/users/constants/users.constants.js'

export class UserHome {
  constructor({
    id = 0,
    first_name = '',
    last_name = '',
    email = '',
    roles = [],
    is_active = true,
    points = 0,
    avatar = '',
    actions = {},
  } = {}) {
    this.id = id || 0
    this.key = `user_${this.id}`
    this.firstName = first_name || ''
    this.lastName = last_name || ''
    this.email = email || ''
    this.roles = (roles || []).map((roleCode, index) => {
      return {
        key: `${this.key}_role${index}`,
        code: USER_ROLE_NAMES[roleCode] || USER_ROLE_NAMES.default,
      }
    })
    this.isUpdating = false
    this.isActive = is_active || false
    this.score = points || '-'
    this.avatar = avatar || ''
    this.actions = {
      impersonate: actions?.allow_login_as || false,
      deletable: actions?.deletable || false,
      editable: actions?.editable || false,
    }
  }
}
