<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Shared\Entity\LifeCycleEntity;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Uuid\Uuid;

class PurchasableItem extends LifeCycleEntity
{
    /**
     * @throws InvalidPurchasableItemException
     */
    public function __construct(
        Uuid $id,
        private readonly string $name,
        private readonly string $description,
        private readonly Money $price,
        private readonly Resource $resource,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ) {
        $this->validatePrice($price);
        parent::__construct(
            id: $id,
            createdAt: $createdAt,
            updatedAt: $updatedAt,
            deletedAt: $deletedAt
        );
    }

    /**
     * @throws InvalidPurchasableItemException
     */
    private function validatePrice(Money $price): void
    {
        if ($price->value() < 0) {
            throw InvalidPurchasableItemException::negativePrice();
        }
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getPrice(): Money
    {
        return $this->price;
    }

    public function getResource(): Resource
    {
        return $this->resource;
    }
}
