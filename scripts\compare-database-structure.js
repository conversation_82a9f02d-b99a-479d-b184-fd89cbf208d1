#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Configuración
const OUTPUT_DIR = './scripts/db-comparison';
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);

// Crear directorio de salida si no existe
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Ejecutar comando y capturar salida
 */
function runCommand(command, description) {
    console.log(`🔄 ${description}...`);
    try {
        const result = execSync(command, { encoding: 'utf8', cwd: process.cwd() });
        console.log(`✅ ${description} completado`);
        return result;
    } catch (error) {
        console.error(`❌ Error en ${description}:`, error.message);
        throw error;
    }
}

/**
 * Generar esquema actual desde entidades Doctrine
 */
function generateCurrentSchema() {
    console.log('\n📋 Generando esquema actual desde entidades...');
    
    // Generar SQL del esquema completo
    const schemaSql = runCommand(
        'docker exec easylearning-backend-php symfony console doctrine:schema:create --dump-sql',
        'Generando SQL del esquema desde entidades'
    );
    
    const schemaFile = path.join(OUTPUT_DIR, `current-schema-${TIMESTAMP}.sql`);
    fs.writeFileSync(schemaFile, schemaSql);
    console.log(`📄 Esquema guardado en: ${schemaFile}`);
    
    return schemaFile;
}

/**
 * Generar diferencias con la base de datos actual
 */
function generateSchemaDiff() {
    console.log('\n🔍 Generando diferencias con la base de datos...');
    
    const diffSql = runCommand(
        'docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete',
        'Generando diferencias de esquema'
    );
    
    const diffFile = path.join(OUTPUT_DIR, `schema-diff-${TIMESTAMP}.sql`);
    fs.writeFileSync(diffFile, diffSql);
    console.log(`📄 Diferencias guardadas en: ${diffFile}`);
    
    return diffFile;
}

/**
 * Validar esquema actual
 */
function validateSchema() {
    console.log('\n✅ Validando esquema...');
    
    try {
        const validation = runCommand(
            'docker exec easylearning-backend-php symfony console doctrine:schema:validate',
            'Validando esquema de base de datos'
        );
        
        const validationFile = path.join(OUTPUT_DIR, `schema-validation-${TIMESTAMP}.txt`);
        fs.writeFileSync(validationFile, validation);
        console.log(`📄 Validación guardada en: ${validationFile}`);
        
        return validation.includes('[OK]');
    } catch (error) {
        return false;
    }
}

/**
 * Exportar estructura de base de datos actual
 */
function exportDatabaseStructure() {
    console.log('\n📤 Exportando estructura de base de datos...');
    
    // Parsear DATABASE_URL
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
        throw new Error('DATABASE_URL no está configurada');
    }
    
    const parsedUrl = url.parse(databaseUrl);
    const username = parsedUrl.auth.split(':')[0];
    const password = parsedUrl.auth.split(':')[1];
    const host = parsedUrl.hostname;
    const database = parsedUrl.pathname.substr(1);
    
    // Exportar solo estructura (sin datos)
    const structureFile = path.join(OUTPUT_DIR, `db-structure-${TIMESTAMP}.sql`);
    
    runCommand(
        `docker exec -i easylearning-database mysqldump -h ${host} -u ${username} -p${password} --no-data --routines --triggers ${database} > ${structureFile}`,
        'Exportando estructura de base de datos'
    );
    
    console.log(`📄 Estructura exportada en: ${structureFile}`);
    return structureFile;
}

/**
 * Listar todas las migraciones
 */
function listMigrations() {
    console.log('\n📋 Listando migraciones...');
    
    const migrations = runCommand(
        'docker exec easylearning-backend-php symfony console doctrine:migrations:list',
        'Listando migraciones'
    );
    
    const migrationsFile = path.join(OUTPUT_DIR, `migrations-status-${TIMESTAMP}.txt`);
    fs.writeFileSync(migrationsFile, migrations);
    console.log(`📄 Estado de migraciones guardado en: ${migrationsFile}`);
    
    return migrations;
}

/**
 * Generar reporte completo
 */
function generateReport(files) {
    const reportFile = path.join(OUTPUT_DIR, `database-comparison-report-${TIMESTAMP}.md`);
    
    const report = `# Reporte de Comparación de Base de Datos
    
Generado el: ${new Date().toLocaleString()}

## Archivos Generados

${files.map(file => `- [${path.basename(file)}](./${path.basename(file)})`).join('\n')}

## Comandos Útiles

### Para aplicar diferencias:
\`\`\`bash
# Ver diferencias
make console doctrine:schema:update --dump-sql --complete

# Aplicar diferencias (¡CUIDADO!)
make console doctrine:schema:update --force --complete
\`\`\`

### Para comparar con cliente:
\`\`\`bash
# 1. Conectar a BD del cliente y exportar estructura
mysqldump -h CLIENT_HOST -u CLIENT_USER -pCLIENT_PASS --no-data --routines --triggers CLIENT_DB > client-structure.sql

# 2. Comparar archivos SQL manualmente o con herramientas como diff
diff current-schema-${TIMESTAMP}.sql client-structure.sql
\`\`\`

### Para sincronizar migraciones:
\`\`\`bash
# Sincronizar metadata de migraciones
make console doctrine:migrations:sync-metadata-storage

# Marcar migraciones como ejecutadas (sin ejecutarlas)
make console doctrine:migrations:version --add --all --no-interaction
\`\`\`

## Notas Importantes

- ⚠️ **SIEMPRE** haz backup antes de aplicar cambios en producción
- ✅ Valida los cambios en un entorno de prueba primero
- 📋 Revisa manualmente las diferencias antes de aplicarlas
- 🔄 Considera crear migraciones personalizadas para cambios complejos
`;

    fs.writeFileSync(reportFile, report);
    console.log(`📊 Reporte completo generado en: ${reportFile}`);
    
    return reportFile;
}

/**
 * Función principal
 */
async function main() {
    console.log('🚀 Iniciando comparación de estructura de base de datos...\n');
    
    try {
        const files = [];
        
        // Generar esquema actual
        files.push(generateCurrentSchema());
        
        // Generar diferencias
        files.push(generateSchemaDiff());
        
        // Validar esquema
        const isValid = validateSchema();
        console.log(`\n📊 Estado del esquema: ${isValid ? '✅ VÁLIDO' : '❌ INVÁLIDO'}`);
        
        // Exportar estructura actual
        files.push(exportDatabaseStructure());
        
        // Listar migraciones
        listMigrations();
        
        // Generar reporte
        const reportFile = generateReport(files);
        
        console.log('\n🎉 Comparación completada exitosamente!');
        console.log(`📊 Ver reporte completo en: ${reportFile}`);
        
    } catch (error) {
        console.error('\n❌ Error durante la comparación:', error.message);
        process.exit(1);
    }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
    main();
}

module.exports = { main };
