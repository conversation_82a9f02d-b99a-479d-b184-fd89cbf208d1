<?php

declare(strict_types=1);

namespace App\Command\Database;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Schema\Comparator;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\SchemaTool;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(
    name: 'app:database:compare-structure',
    description: 'Compara la estructura de entidades Doctrine y tablas V2 con una base de datos'
)]
class CompareStructureCommand extends Command
{
    // Tablas V2 definidas en config/services/v2.yaml
    private const V2_TABLES = [
        'course_creator',
        'announcement_manager',
        'purchasable_item',
        'subscription',
        'lti_registration',
        'lti_platform',
        'lti_tool_v2',
        'lti_deployment',
        'user_filter',
        'manager_filter',
    ];

    public function __construct(
        private EntityManagerInterface $entityManager,
        private Connection $connection,
        private ParameterBagInterface $parameterBag
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('remote-dsn', InputArgument::OPTIONAL, 'DSN de la base de datos remota (mysql://user:pass@host:port/database). Si no se proporciona, usa la BD local')
            ->addOption('output-file', 'o', InputOption::VALUE_OPTIONAL, 'Archivo donde guardar el reporte')
            ->addOption('format', 'f', InputOption::VALUE_OPTIONAL, 'Formato de salida (text|json|sql)', 'text')
            ->addOption('only-differences', null, InputOption::VALUE_NONE, 'Mostrar solo las diferencias')
            ->addOption('include-v2', null, InputOption::VALUE_NONE, 'Incluir análisis de tablas V2')
            ->addOption('v2-only', null, InputOption::VALUE_NONE, 'Analizar solo tablas V2')
            ->setHelp('
Este comando compara la estructura definida en las entidades Doctrine y tablas V2
con una base de datos local o remota y muestra las diferencias.

Ejemplos:
  # Comparar con base de datos local (Doctrine + V2)
  php bin/console app:database:compare-structure --include-v2

  # Comparar con base de datos remota
  php bin/console app:database:compare-structure mysql://user:<EMAIL>:3306/client_db --include-v2

  # Solo analizar tablas V2
  php bin/console app:database:compare-structure --v2-only

  # Guardar reporte en archivo
  php bin/console app:database:compare-structure --include-v2 -o report.txt

  # Solo mostrar diferencias
  php bin/console app:database:compare-structure --include-v2 --only-differences
            ');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $remoteDsn = $input->getArgument('remote-dsn');
        $outputFile = $input->getOption('output-file');
        $format = $input->getOption('format');
        $onlyDifferences = $input->getOption('only-differences');
        $includeV2 = $input->getOption('include-v2');
        $v2Only = $input->getOption('v2-only');

        $io->title('Comparación de Estructura de Base de Datos (Doctrine + V2)');

        try {
            // Determinar qué base de datos usar
            $targetConnection = $remoteDsn ? $this->createRemoteConnection($remoteDsn) : $this->connection;
            $targetLabel = $remoteDsn ? 'remota' : 'local';

            $results = [];

            // 1. Análisis Doctrine (si no es solo V2)
            if (!$v2Only) {
                $io->section('Analizando esquema Doctrine...');
                $localSchema = $this->getLocalSchema();
                $targetSchema = $this->getRemoteSchema($targetConnection);
                $doctrineDifferences = $this->compareSchemas($localSchema, $targetSchema);

                $results['doctrine'] = [
                    'local_schema' => $localSchema,
                    'target_schema' => $targetSchema,
                    'differences' => $doctrineDifferences
                ];

                $io->success(sprintf('Doctrine: %d tablas locales vs %d tablas %s',
                    count($localSchema->getTables()),
                    count($targetSchema->getTables()),
                    $targetLabel
                ));
            }

            // 2. Análisis V2 (si está habilitado)
            if ($includeV2 || $v2Only) {
                $io->section('Analizando tablas V2...');
                $v2Analysis = $this->analyzeV2Tables($targetConnection);
                $results['v2'] = $v2Analysis;

                $io->success(sprintf('V2: %d tablas definidas, %d encontradas en BD %s',
                    count(self::V2_TABLES),
                    count($v2Analysis['existing_tables']),
                    $targetLabel
                ));
            }

            // 3. Generar reporte completo
            $report = $this->generateCompleteReport($results, $format, $onlyDifferences, $targetLabel);

            // 4. Mostrar/guardar resultado
            if ($outputFile) {
                file_put_contents($outputFile, $report);
                $io->success("Reporte guardado en: $outputFile");
            } else {
                $output->write($report);
            }

            // 5. Resumen
            $this->showCompleteSummary($io, $results, $onlyDifferences);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Error durante la comparación: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function getLocalSchema(): Schema
    {
        $metadata = $this->entityManager->getMetadataFactory()->getAllMetadata();
        $schemaTool = new SchemaTool($this->entityManager);
        
        return $schemaTool->getSchemaFromMetadata($metadata);
    }

    private function createRemoteConnection(string $dsn): Connection
    {
        $params = $this->parseDsn($dsn);
        
        return \Doctrine\DBAL\DriverManager::getConnection($params);
    }

    private function parseDsn(string $dsn): array
    {
        $parsed = parse_url($dsn);
        
        if (!$parsed) {
            throw new \InvalidArgumentException("DSN inválido: $dsn");
        }

        return [
            'driver' => $parsed['scheme'] === 'mysql' ? 'pdo_mysql' : $parsed['scheme'],
            'host' => $parsed['host'],
            'port' => $parsed['port'] ?? 3306,
            'dbname' => ltrim($parsed['path'], '/'),
            'user' => $parsed['user'],
            'password' => $parsed['pass'] ?? '',
            'charset' => 'utf8mb4',
        ];
    }

    private function getRemoteSchema(Connection $connection): Schema
    {
        return $connection->createSchemaManager()->introspectSchema();
    }

    private function compareSchemas(Schema $localSchema, Schema $remoteSchema): array
    {
        $comparator = new Comparator();
        $schemaDiff = $comparator->compareSchemas($remoteSchema, $localSchema);

        return [
            'new_tables' => $schemaDiff->getCreatedTables(),
            'dropped_tables' => $schemaDiff->getDroppedTables(),
            'altered_tables' => $schemaDiff->getAlteredTables(),
            'new_sequences' => $schemaDiff->getCreatedSequences(),
            'dropped_sequences' => $schemaDiff->getDroppedSequences(),
            'altered_sequences' => $schemaDiff->getAlteredSequences(),
        ];
    }

    private function generateReport(Schema $localSchema, Schema $remoteSchema, array $differences, string $format, bool $onlyDifferences): string
    {
        switch ($format) {
            case 'json':
                return $this->generateJsonReport($localSchema, $remoteSchema, $differences, $onlyDifferences);
            case 'sql':
                return $this->generateSqlReport($differences);
            default:
                return $this->generateTextReport($localSchema, $remoteSchema, $differences, $onlyDifferences);
        }
    }

    private function generateTextReport(Schema $localSchema, Schema $remoteSchema, array $differences, bool $onlyDifferences): string
    {
        $report = [];
        
        if (!$onlyDifferences) {
            $report[] = "=== REPORTE DE COMPARACIÓN DE ESTRUCTURA ===";
            $report[] = "Fecha: " . date('Y-m-d H:i:s');
            $report[] = "";
            $report[] = "Tablas locales: " . count($localSchema->getTables());
            $report[] = "Tablas remotas: " . count($remoteSchema->getTables());
            $report[] = "";
        }

        // Tablas nuevas
        if (!empty($differences['new_tables'])) {
            $report[] = "🆕 TABLAS NUEVAS (en local, no en remoto):";
            foreach ($differences['new_tables'] as $table) {
                $report[] = "  + " . $table->getName();
            }
            $report[] = "";
        }

        // Tablas eliminadas
        if (!empty($differences['dropped_tables'])) {
            $report[] = "🗑️ TABLAS ELIMINADAS (en remoto, no en local):";
            foreach ($differences['dropped_tables'] as $tableName) {
                $report[] = "  - " . $tableName;
            }
            $report[] = "";
        }

        // Tablas modificadas
        if (!empty($differences['altered_tables'])) {
            $report[] = "🔧 TABLAS MODIFICADAS:";
            foreach ($differences['altered_tables'] as $tableDiff) {
                $report[] = "  ~ " . $tableDiff->getOldTable()->getName();
                
                // Columnas añadidas
                foreach ($tableDiff->getAddedColumns() as $column) {
                    $report[] = "    + Columna: " . $column->getName() . " (" . $column->getType()->getName() . ")";
                }
                
                // Columnas eliminadas
                foreach ($tableDiff->getDroppedColumns() as $columnName) {
                    $report[] = "    - Columna: " . $columnName;
                }
                
                // Columnas modificadas
                foreach ($tableDiff->getModifiedColumns() as $columnDiff) {
                    $report[] = "    ~ Columna: " . $columnDiff->getOldColumn()->getName() . " (modificada)";
                }
            }
            $report[] = "";
        }

        if (empty($differences['new_tables']) && empty($differences['dropped_tables']) && empty($differences['altered_tables'])) {
            $report[] = "✅ Las estructuras son idénticas";
        }

        return implode("\n", $report);
    }

    private function generateJsonReport(Schema $localSchema, Schema $remoteSchema, array $differences, bool $onlyDifferences): string
    {
        $data = [
            'timestamp' => date('c'),
            'local_tables_count' => count($localSchema->getTables()),
            'remote_tables_count' => count($remoteSchema->getTables()),
            'differences' => [
                'new_tables' => array_map(fn($table) => $table->getName(), $differences['new_tables']),
                'dropped_tables' => $differences['dropped_tables'],
                'altered_tables' => array_map(fn($tableDiff) => $tableDiff->getOldTable()->getName(), $differences['altered_tables']),
            ]
        ];

        if (!$onlyDifferences) {
            $data['local_tables'] = array_map(fn($table) => $table->getName(), $localSchema->getTables());
            $data['remote_tables'] = array_map(fn($table) => $table->getName(), $remoteSchema->getTables());
        }

        return json_encode($data, JSON_PRETTY_PRINT);
    }

    private function generateSqlReport(array $differences): string
    {
        $sql = [];
        $sql[] = "-- SQL para sincronizar estructura";
        $sql[] = "-- Generado el: " . date('Y-m-d H:i:s');
        $sql[] = "";

        // Aquí podrías generar SQL específico basado en las diferencias
        // Por simplicidad, solo agregamos comentarios
        
        if (!empty($differences['new_tables'])) {
            $sql[] = "-- Tablas nuevas a crear:";
            foreach ($differences['new_tables'] as $table) {
                $sql[] = "-- CREATE TABLE " . $table->getName() . " (...);";
            }
            $sql[] = "";
        }

        return implode("\n", $sql);
    }

    private function analyzeV2Tables(Connection $connection): array
    {
        $existingTables = [];
        $missingTables = [];
        $tableStructures = [];

        foreach (self::V2_TABLES as $tableName) {
            try {
                // Verificar si la tabla existe
                $schemaManager = $connection->createSchemaManager();
                if ($schemaManager->tablesExist([$tableName])) {
                    $existingTables[] = $tableName;

                    // Obtener estructura de la tabla
                    $table = $schemaManager->introspectTable($tableName);
                    $tableStructures[$tableName] = [
                        'columns' => array_keys($table->getColumns()),
                        'indexes' => array_keys($table->getIndexes()),
                        'foreign_keys' => array_keys($table->getForeignKeys()),
                    ];
                } else {
                    $missingTables[] = $tableName;
                }
            } catch (\Exception $e) {
                $missingTables[] = $tableName;
            }
        }

        return [
            'existing_tables' => $existingTables,
            'missing_tables' => $missingTables,
            'table_structures' => $tableStructures,
        ];
    }

    private function generateCompleteReport(array $results, string $format, bool $onlyDifferences, string $targetLabel): string
    {
        switch ($format) {
            case 'json':
                return $this->generateCompleteJsonReport($results, $onlyDifferences, $targetLabel);
            case 'sql':
                return $this->generateCompleteSqlReport($results);
            default:
                return $this->generateCompleteTextReport($results, $onlyDifferences, $targetLabel);
        }
    }

    private function generateCompleteTextReport(array $results, bool $onlyDifferences, string $targetLabel): string
    {
        $report = [];

        if (!$onlyDifferences) {
            $report[] = "=== REPORTE COMPLETO DE COMPARACIÓN DE ESTRUCTURA ===";
            $report[] = "Fecha: " . date('Y-m-d H:i:s');
            $report[] = "Base de datos objetivo: $targetLabel";
            $report[] = "";
        }

        // Reporte Doctrine
        if (isset($results['doctrine'])) {
            $report[] = "🔧 ANÁLISIS DOCTRINE";
            $report[] = "==================";

            $differences = $results['doctrine']['differences'];
            $localSchema = $results['doctrine']['local_schema'];
            $targetSchema = $results['doctrine']['target_schema'];

            if (!$onlyDifferences) {
                $report[] = "Tablas locales: " . count($localSchema->getTables());
                $report[] = "Tablas en BD $targetLabel: " . count($targetSchema->getTables());
                $report[] = "";
            }

            $report = array_merge($report, $this->generateDoctrineDifferencesReport($differences));
        }

        // Reporte V2
        if (isset($results['v2'])) {
            $report[] = "";
            $report[] = "🚀 ANÁLISIS TABLAS V2";
            $report[] = "====================";

            $v2Analysis = $results['v2'];

            if (!$onlyDifferences) {
                $report[] = "Tablas V2 definidas: " . count(self::V2_TABLES);
                $report[] = "Tablas V2 encontradas: " . count($v2Analysis['existing_tables']);
                $report[] = "";
            }

            // Tablas V2 existentes
            if (!empty($v2Analysis['existing_tables'])) {
                $report[] = "✅ TABLAS V2 EXISTENTES:";
                foreach ($v2Analysis['existing_tables'] as $tableName) {
                    $structure = $v2Analysis['table_structures'][$tableName] ?? [];
                    $columnCount = count($structure['columns'] ?? []);
                    $report[] = "  ✓ $tableName ($columnCount columnas)";
                }
                $report[] = "";
            }

            // Tablas V2 faltantes
            if (!empty($v2Analysis['missing_tables'])) {
                $report[] = "❌ TABLAS V2 FALTANTES:";
                foreach ($v2Analysis['missing_tables'] as $tableName) {
                    $report[] = "  ✗ $tableName";
                }
                $report[] = "";
            }

            // Detalles de estructura (si no es solo diferencias)
            if (!$onlyDifferences && !empty($v2Analysis['table_structures'])) {
                $report[] = "📋 ESTRUCTURA DETALLADA DE TABLAS V2:";
                foreach ($v2Analysis['table_structures'] as $tableName => $structure) {
                    $report[] = "  📄 $tableName:";
                    $report[] = "    Columnas: " . implode(', ', $structure['columns']);
                    if (!empty($structure['indexes'])) {
                        $report[] = "    Índices: " . implode(', ', $structure['indexes']);
                    }
                    if (!empty($structure['foreign_keys'])) {
                        $report[] = "    Claves foráneas: " . implode(', ', $structure['foreign_keys']);
                    }
                    $report[] = "";
                }
            }
        }

        return implode("\n", $report);
    }

    private function generateDoctrineDifferencesReport(array $differences): array
    {
        $report = [];

        // Tablas nuevas
        if (!empty($differences['new_tables'])) {
            $report[] = "🆕 TABLAS NUEVAS (en local, no en BD objetivo):";
            foreach ($differences['new_tables'] as $table) {
                $report[] = "  + " . $table->getName();
            }
            $report[] = "";
        }

        // Tablas eliminadas
        if (!empty($differences['dropped_tables'])) {
            $report[] = "🗑️ TABLAS ELIMINADAS (en BD objetivo, no en local):";
            foreach ($differences['dropped_tables'] as $tableName) {
                $report[] = "  - $tableName";
            }
            $report[] = "";
        }

        // Tablas modificadas
        if (!empty($differences['altered_tables'])) {
            $report[] = "🔧 TABLAS MODIFICADAS:";
            foreach ($differences['altered_tables'] as $tableDiff) {
                $report[] = "  ~ " . $tableDiff->getOldTable()->getName();

                // Columnas añadidas
                foreach ($tableDiff->getAddedColumns() as $column) {
                    $report[] = "    + Columna: " . $column->getName() . " (" . $column->getType()->getName() . ")";
                }

                // Columnas eliminadas
                foreach ($tableDiff->getDroppedColumns() as $columnName) {
                    $report[] = "    - Columna: $columnName";
                }

                // Columnas modificadas
                foreach ($tableDiff->getModifiedColumns() as $columnDiff) {
                    $report[] = "    ~ Columna: " . $columnDiff->getOldColumn()->getName() . " (modificada)";
                }
            }
            $report[] = "";
        }

        if (empty($differences['new_tables']) && empty($differences['dropped_tables']) && empty($differences['altered_tables'])) {
            $report[] = "✅ Las estructuras Doctrine son idénticas";
        }

        return $report;
    }

    private function showCompleteSummary(SymfonyStyle $io, array $results, bool $onlyDifferences): void
    {
        // Resumen Doctrine
        if (isset($results['doctrine'])) {
            $differences = $results['doctrine']['differences'];
            $newTables = count($differences['new_tables']);
            $droppedTables = count($differences['dropped_tables']);
            $alteredTables = count($differences['altered_tables']);

            if ($newTables === 0 && $droppedTables === 0 && $alteredTables === 0) {
                $io->success('✅ Doctrine: Las estructuras son idénticas');
            } else {
                $io->warning('⚠️ Doctrine: Se encontraron diferencias:');
                if ($newTables > 0) $io->text("  🆕 Tablas nuevas: $newTables");
                if ($droppedTables > 0) $io->text("  🗑️ Tablas eliminadas: $droppedTables");
                if ($alteredTables > 0) $io->text("  🔧 Tablas modificadas: $alteredTables");
            }
        }

        // Resumen V2
        if (isset($results['v2'])) {
            $v2Analysis = $results['v2'];
            $existingCount = count($v2Analysis['existing_tables']);
            $missingCount = count($v2Analysis['missing_tables']);
            $totalV2Tables = count(self::V2_TABLES);

            if ($missingCount === 0) {
                $io->success("✅ V2: Todas las tablas ($existingCount/$totalV2Tables) están presentes");
            } else {
                $io->warning("⚠️ V2: Faltan $missingCount de $totalV2Tables tablas:");
                foreach ($v2Analysis['missing_tables'] as $tableName) {
                    $io->text("  ❌ $tableName");
                }
            }
        }
    }

    private function generateCompleteJsonReport(array $results, bool $onlyDifferences, string $targetLabel): string
    {
        $data = [
            'timestamp' => date('c'),
            'target' => $targetLabel,
        ];

        if (isset($results['doctrine'])) {
            $localSchema = $results['doctrine']['local_schema'];
            $targetSchema = $results['doctrine']['target_schema'];
            $differences = $results['doctrine']['differences'];

            $data['doctrine'] = [
                'local_tables_count' => count($localSchema->getTables()),
                'target_tables_count' => count($targetSchema->getTables()),
                'differences' => [
                    'new_tables' => array_map(fn($table) => $table->getName(), $differences['new_tables']),
                    'dropped_tables' => $differences['dropped_tables'],
                    'altered_tables' => array_map(fn($tableDiff) => $tableDiff->getOldTable()->getName(), $differences['altered_tables']),
                ]
            ];

            if (!$onlyDifferences) {
                $data['doctrine']['local_tables'] = array_map(fn($table) => $table->getName(), $localSchema->getTables());
                $data['doctrine']['target_tables'] = array_map(fn($table) => $table->getName(), $targetSchema->getTables());
            }
        }

        if (isset($results['v2'])) {
            $data['v2'] = $results['v2'];
        }

        return json_encode($data, JSON_PRETTY_PRINT);
    }

    private function generateCompleteSqlReport(array $results): string
    {
        $sql = [];
        $sql[] = "-- SQL para sincronizar estructura completa (Doctrine + V2)";
        $sql[] = "-- Generado el: " . date('Y-m-d H:i:s');
        $sql[] = "";

        if (isset($results['doctrine'])) {
            $sql[] = "-- === CAMBIOS DOCTRINE ===";
            // Aquí podrías generar SQL específico para Doctrine
            $sql[] = "-- Usar: php bin/console doctrine:schema:update --dump-sql";
            $sql[] = "";
        }

        if (isset($results['v2'])) {
            $sql[] = "-- === TABLAS V2 FALTANTES ===";
            $v2Analysis = $results['v2'];
            foreach ($v2Analysis['missing_tables'] as $tableName) {
                $sql[] = "-- Crear tabla: $tableName";
                $sql[] = "-- TODO: Definir estructura de $tableName";
                $sql[] = "";
            }
        }

        return implode("\n", $sql);
    }

    private function showSummary(SymfonyStyle $io, array $differences, bool $onlyDifferences): void
    {
        $newTables = count($differences['new_tables']);
        $droppedTables = count($differences['dropped_tables']);
        $alteredTables = count($differences['altered_tables']);

        if ($newTables === 0 && $droppedTables === 0 && $alteredTables === 0) {
            $io->success('✅ Las estructuras son idénticas');
        } else {
            $io->warning('⚠️ Se encontraron diferencias:');
            if ($newTables > 0) $io->text("  🆕 Tablas nuevas: $newTables");
            if ($droppedTables > 0) $io->text("  🗑️ Tablas eliminadas: $droppedTables");
            if ($alteredTables > 0) $io->text("  🔧 Tablas modificadas: $alteredTables");
        }
    }
}
